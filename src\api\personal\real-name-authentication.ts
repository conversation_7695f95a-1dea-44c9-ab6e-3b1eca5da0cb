import request from '@/service/axios'

/**
 * @file 个人设置-实名认证相关API
 */

/** 平台门户实名认证中的个人认证 首页及详情 */
export const getRealNameAuthentication = (userId: string) => request.get({ url: `/user-center/user/realname-detail?id=${userId}` })
/** 提交企业认证 */
export const addEnterpriseAuthentication = (data: any) => request.post({ url: '/user-center/enterprise/certification/submit', data })
/** 获取企业认证信息 基础信息& 工商信息 */
export const getEnterpriseDetails = (enterpriseId: string) => request.get({ url: `/user-center/enterprise/certification/info/${enterpriseId}` })
/** 我认证的企业 暂时按一家企业实现 */
export const getMyEnterpriseDetails = () => request.get({ url: `/user-center/enterprise/certification/myEnterprise` })
/** 提交个人认证 */
export const addPersonalAuthentication = (data: any) => request.post({ url: '/user-center/realname/auditlog/create', data })
/** 获取区域列表（省市区） */
export const getAreaList = (id?: string) => request.get({ url: `/tenant/base/region/get-children-by-id`, params: { id } })
