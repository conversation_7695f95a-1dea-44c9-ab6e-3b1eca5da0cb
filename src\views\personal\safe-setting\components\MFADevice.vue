<script setup lang="ts">
import { ElMessage } from 'element-plus'

function handleBinding() {
  ElMessage.warning('该功能后续开发')
}
</script>

<template>
  <div>
    <div class="content">
      <span class="title">MFA设备</span>
      <span class="info">未绑定MFA设备，绑定后，可以进行二次确认</span>
    </div>
    <el-button
      type="primary"
      link
      @click="handleBinding"
    >
      绑定
    </el-button>
  </div>
</template>

<style lang="scss" scoped></style>
