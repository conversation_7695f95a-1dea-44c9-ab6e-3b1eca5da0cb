<script setup lang="ts">
import { updateNotificationSetting } from '@/api/personal/notification-setting'

interface Prop {
  isObtained?: boolean
}

const props = defineProps<Prop>()

const enabled = defineModel<EnableNum>({ required: true, default: 0 })
function handleSwitchBeforeChange(): Promise<boolean> {
  return new Promise((resolve, reject) => {
    updateNotificationSetting({ systemNotify: enabled.value === 0 ? 1 : 0 })
      .then(() => {
        $message.success(`${enabled.value === 0 ? '关闭' : '开启'}系统通知成功`)
        resolve(true)
      })
      .catch(() => {
        // eslint-disable-next-line prefer-promise-reject-errors
        reject(false)
      })
  })
}
</script>

<template>
  <div class="system-notification">
    <div class="content">
      <span class="title">系统通知</span>
      <span class="info">系统消息将以站内信的形式通知</span>
    </div>
    <el-switch
      v-if="props.isObtained"
      v-model="enabled"
      :active-value="0"
      :inactive-value="1"
      :before-change="handleSwitchBeforeChange"
    />
  </div>
</template>

<style lang="scss" scoped>
// .system-notification {
// }
</style>
