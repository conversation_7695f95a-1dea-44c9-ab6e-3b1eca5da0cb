# 定义安装依赖的作业
install_dependencies:
  stage: install
  tags:
    - pnpm
  script:
    - echo ">>>>>>>>>>>>>>>>>>>>开始安装依赖<<<<<<<<<<<<<<<<<<<<<<<<<<<<"
    - pnpm install
  only:
    - develop
  cache:
    key: ${CI_PROJECT_NAME}
    paths:
      - node_modules/
      - packages/utils/node_modules/

# 构建项目
build_main_project:
  stage: build
  tags:
    - pnpm
  dependencies:
    - install_dependencies
  needs:
    - install_dependencies
  when: manual
  script:
    - pnpm run build:dev # 执行构建命令
  artifacts:
    paths:
      - dist/ # 保存构建后的输出
    expire_in: 1 week # 构建产物保存一周
  only:
    - develop
  cache:
    key: ${CI_PROJECT_NAME}
    paths:
      - node_modules/
      - packages/utils/node_modules/

# 构建镜像
build_main_image:
  stage: build_img
  tags:
    - host-docker-builder
  dependencies:
    - build_main_project
  needs:
    - build_main_project
  before_script:
    - echo "$DOCKER_REGISTRY_PASS" | docker login $DOCKER_REGISTRY --username $DOCKER_REGISTRY_USER --password-stdin
  script:
    - docker build -t $DOCKER_REGISTRY/recircle-industry-platform/$IMAGE_NAME:dev .
    - docker push $DOCKER_REGISTRY/recircle-industry-platform/$IMAGE_NAME:dev
  only:
    - develop

# 部署上K8S
deploy_main_k8s:
  stage: deploy
  tags:
    - kube-deployer
  dependencies:
    - build_main_image
  needs:
    - build_main_image
  script:
    - kubectl patch deployment $IMAGE_NAME-v1 --context=<EMAIL> -n recircle-industry-platform-dev -p "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"kubectl.kubernetes.io/restartedAt\":\"$(date +%Y-%m-%dT%H:%M:%S)\"}}}}}"
  only:
    - develop
