<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { type PasswordStrength, updatePassword } from '@/api/personal/safe-setting'
import SkTipDialog from '@/components/TipDialog/SkTipDialog.vue'
import { REG } from '@/constant/reg'
import { useGetCssVariable } from '@/hooks/useGetCssVariable'
import { useUserStore } from '@/store/modules/user'
import { cryptoJS } from '@2030/utils'

interface Prop {
  passwordStrength: PasswordStrength
}

const props = defineProps<Prop>()

const { getVariableValue } = useGetCssVariable()

const { logout } = useUserStore()

// 密码强度等级 1 强 2 中 3 弱
const level = ref(props.passwordStrength)
watchEffect(() => {
  level.value = props.passwordStrength
})

function getLevelText(level: number) {
  switch (level) {
    case 1:
      return '强'
    case 2:
      return '中'
    case 3:
      return '弱'
    default:
      return ''
  }
}

const formRef = useTemplateRef<FormInstance>('formRef')
const form = reactive({
  password: '',
  rePassword: '',
})

const isShowDialog = ref(false)

function getTypesCount(value: string) {
  const hasLetter = REG.HAS_LETTER.test(value)
  const hasNumber = REG.HAS_NUMBER.test(value)
  const hasSpecialChar = REG.HAS_SPECIAL_CHAR.test(value)
  return Number(hasLetter) + Number(hasNumber) + Number(hasSpecialChar)
}
function validatePassword(_rule: any, value: string, callback: (error?: string | Error) => void) {
  if (!REG.NUM_ENG_SYM.test(value)) {
    callback(new Error('密码必须为字母、数字、下划线'))
    return
  }

  if (value.length < 4 || value.length > 16) {
    callback(new Error('密码长度必须在4-16位之间'))
    return
  }

  if (getTypesCount(value) < 2) {
    callback(new Error('密码必须包含字母、数字和下划线中的至少两种'))
    return
  }

  callback()
}
function validateRepeat(_rule: any, value: string, callback: (error?: string | Error) => void) {
  if (value !== form.password) {
    callback(new Error('两次密码输入不一致'))
    return
  }
  callback()
}
const rules = reactive<FormRules<typeof form>>({
  password: [
    { required: true, message: '请输入', trigger: 'blur' },
    { validator: validatePassword, trigger: 'blur' },
  ],
  rePassword: [
    { required: true, message: '请输入', trigger: 'blur' },
    { validator: validateRepeat, trigger: 'blur' },
  ],
})

const progressInfo = reactive({
  1: 100,
  2: 0,
  3: 0,
  color: getVariableValue('--el-color-warning'),
  level: 3 as PasswordStrength,
  levelText: '弱',
})
function setPasswordStrength(level: PasswordStrength) {
  progressInfo.level = level
  progressInfo.levelText = getLevelText(level)
  progressInfo.color = level === 1 ? getVariableValue('--el-color-success') : level === 2 ? getVariableValue('--el-color-warning') : getVariableValue('--el-color-error')
  progressInfo['1'] = 100
  progressInfo['2'] = level === 3 ? 0 : 100
  progressInfo['3'] = level === 1 ? 100 : 0
}
function handleNewPasswordInput() {
  // 禁止输入空格
  form.password = form.password.replace(/\s+/g, '')

  /**
   * 密码弱中强规则：
   * 1：弱密码：单纯某两种组成；如：333AAA     222sss
   * 2：中密码：三种混合长度低于等于8位
   * 3：强密码：三种混合长度超过8位
   */
  if (!form.password)
    return
  if (getTypesCount(form.password) <= 2)
    setPasswordStrength(3)
  else if (form.password.length <= 8)
    setPasswordStrength(2)
  else setPasswordStrength(1)
}

function handleCancel() {
  form.password = ''
  form.rePassword = ''
  formRef.value?.resetFields()
}

function handleConfirm(done: () => void, close: () => void) {
  formRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        await updatePassword({ password: cryptoJS.encryptMD5(form.password), rePassword: cryptoJS.encryptMD5(form.rePassword) })
        $message.success('修改成功，请重新登录')
        level.value = progressInfo.level
        close()
        logout()
      } finally {
        done()
      }
    } else {
      done()
    }
  })
}
</script>

<template>
  <div class="account-password">
    <div class="content">
      <span class="title">账户密码</span>
      <span class="info">
        当前密码强度
        <span
          class="level ml-20"
          :data-level="level"
        >
          {{ getLevelText(level) }}
        </span>
      </span>
    </div>
    <el-button
      type="primary"
      link
      @click="isShowDialog = true"
    >
      修改
    </el-button>
    <SkTipDialog
      v-model="isShowDialog"
      head-title="修改账户密码"
      :is-confirm-tip="false"
      :foot-btn-config="{ isShowCancelBtn: false }"
      @cancel="handleCancel"
      @confirm="handleConfirm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item
          label="新密码："
          prop="password"
        >
          <el-input
            v-model="form.password"
            placeholder="请输入"
            clearable
            @input="handleNewPasswordInput"
          />
        </el-form-item>
        <el-form-item
          v-if="form.password"
          class="password-strength-box"
        >
          <div class="password-strength">
            <el-progress
              v-for="item in ['1', '2', '3']"
              :key="item"
              :percentage="(progressInfo[item as keyof typeof progressInfo] as number)"
              :show-text="false"
              :color="progressInfo.color"
            />
            <span
              class="level"
              :data-level="progressInfo.level"
            >
              {{ getLevelText(progressInfo.level) }}
            </span>
          </div>
        </el-form-item>
        <el-form-item
          label="确认新密码："
          prop="rePassword"
          class="mb-14"
        >
          <el-input
            v-model="form.rePassword"
            placeholder="请输入"
            clearable
            @input="() => (form.rePassword = form.rePassword.replace(/\s+/g, ''))"
          />
        </el-form-item>
      </el-form>
    </SkTipDialog>
  </div>
</template>

<style lang="scss" scoped>
$colors: (
  1: var(--el-color-success),
  2: var(--el-color-warning),
  3: var(--el-color-error),
);

.account-password {
  @for $i from 1 through 3 {
    .level[data-level='#{$i}'] {
      color: map-get($colors, $i);
    }
  }

  :deep(.el-dialog__body) {
    padding: 20px 52px 0 44px !important;
  }

  .mb-14 {
    margin-bottom: 14px;
  }

  .ml-20 {
    margin-left: 20px;
  }

  .password-strength-box {
    margin-bottom: 14px;

    .password-strength {
      height: 16px;
      display: flex;
      align-items: center;

      :deep(.el-progress) {
        width: 60px;
        height: 5px;
        margin-right: 2px;

        .el-progress-bar__outer {
          border-radius: 0 !important;
          background-color: #f3f3f3;

          .el-progress-bar__inner {
            border-radius: 0 !important;
          }
        }
      }

      :deep(.el-progress:last-of-type) {
        margin-right: 5px;
      }
    }
  }
}
</style>
