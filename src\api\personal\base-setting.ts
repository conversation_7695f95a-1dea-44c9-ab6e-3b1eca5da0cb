import request from '@/service/axios'

/**
 * @file 个人设置-基本设置相关API
 */

/** 修改用户基本信息参数类型 */
export interface UpdateAccountInfoReq {
  /** 头像 */
  avatar?: string
  /** 昵称 */
  nickname?: string
  /** 电子邮箱 */
  email?: string
  /** 联系电话 */
  telephone?: string
  /** 国家地区 */
  // countries?: string
  /** 所在省 */
  province?: string
  /** 所在市 */
  city?: string
  /** 街道地址 */
  street?: string
  /** 个人简介 */
  intro?: string
  /** 账号 */
  username?: string
  name?: string
  mobile?: string
  realName: boolean
}

/** 用户基本信息类型 */
export type AccountBaseInfoRes = Required<UpdateAccountInfoReq> & {
  id?: string
  area?: string
  countries?: string
  [property: string]: unknown
}

/** 获取用户账号信息 */
export const getAccountInfo = () => request.get<AccountBaseInfoRes>({ url: '/user-center/user/get-info' })

/** 修改用户账号信息 */
export const updateAccountInfo = (data: UpdateAccountInfoReq) => request.put<boolean>({ url: '/user-center/user/update', data })
