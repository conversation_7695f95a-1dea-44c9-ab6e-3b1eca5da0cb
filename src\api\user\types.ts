/** 用户注册请求参数 */
export interface RegisterReqData {
  username: string // 用户账号
  mobile: string // 手机号码
  captchaCode: string // 验证码
  password: string // 用户密码
  rePassword: string // 确定密码
  sourceType: number // 来源类型
}

/** 登录验证码响应数据 */
export type LoginCodeResData = ResData<string>

/** 忘记密码请求参数 */
export interface ForgetPasswordReqData {
  username: string // 用户名
  mobile: string // 手机号
  captchaCode: string // 验证码
}

/** 账号密码登录请求参数 */
export interface LoginPasswordReqData {
  username: string // 用户名
  password: string // 密码
  [key: string]: unknown // 其它参数
}

/** 手机号登录请求参数 */
export interface LoginMobileReqData {
  mobile: string // 手机号
  captchaCode: string // 验证码
  [key: string]: unknown // 其它参数
}

export interface LoginResData {
  userId: string // 用户ID
  accessToken: string // 访问令牌
  refreshToken: string // 刷新令牌
  expiresTime?: number // 过期时间
}

export type UserInfoResData = ResData<{ username: string, roles: string[] }>
