* {
  margin: 0;
  padding: 0;
}

.app-full {
  height: 100%;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0);
  --drop-color: #1890ff;
}

.loading-drop {
  position: absolute;
  width: 80px;
  height: 80px;
  border-radius: 80% 0 55% 50% / 55% 0 80% 50%;
  background-color: var(--drop-color);
  animation: 3.2s down linear infinite;
  animation-fill-mode: backwards;
}

.loading-drop:nth-of-type(1) {
  animation-delay: 0s;
}

.loading-drop:nth-of-type(2) {
  margin-left: 60px;
  animation-delay: 0.4s;
}

.loading-drop:nth-of-type(3) {
  margin-right: 30px;
  animation-delay: 0.7s;
}

.loading-collection {
  width: 120px;
  height: 120px;
  background-color: var(--drop-color);
  animation: 8s spin linear infinite;
  border-radius: 50%;
  position: relative;
}

.loading-text {
  padding-top: 40px;
  font-size: 32px;
  font-weight: bold;
  color: var(--drop-color);
}

@keyframes down {
  0% {
    opacity: 0;
    filter: blur(2px);
    transform: translateY(-600%) scale(0.3) rotate(-45deg);
  }
  50% {
    opacity: 1;
    filter: blur(1px);
    transform: translateY(-100%) scale(0.5) rotate(-45deg);
  }
  100% {
    opacity: 0;
    filter: blur(0);
    transform: translateY(20%) scale(0.7) rotate(-45deg);
  }
}

@keyframes spin {
  0% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.15) rotate(180deg);
    border-radius: 40% 10% 50% 45%;
  }
  100% {
    transform: scale(1) rotate(360deg);
  }
}
