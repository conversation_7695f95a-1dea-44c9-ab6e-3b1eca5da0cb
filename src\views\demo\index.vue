<script setup lang="tsx">
import ICON_LOGO from '@/assets/imgs/icon.png'
import { LocaleEnum } from '@/enum'
import 'vue-json-pretty/lib/styles.css'

const { t } = useI18n()
const msg = computed(() => t('app.name'))

function toggleLocale() {
  useAppStore().SET_LOCALE(useAppStore().locale === LocaleEnum.ZH_CN ? LocaleEnum.EN_US : LocaleEnum.ZH_CN)
}

const toggleLocaleText = computed(() => {
  return `${t('demo.toggleLocale')}${useAppStore().locale === LocaleEnum.ZH_CN ? t('app.en') : t('app.zh-cn')}`
})

const value1 = ref<[Date, Date]>([
  new Date(2016, 9, 10, 8, 40),
  new Date(2016, 9, 10, 9, 40),
])

interface RowVO {
  id: number
  name: string
  role: string
  sex: string
  age: number
  address: string
}

const tableData = ref<RowVO[]>([])
</script>

<template>
  <div>
    <p>{{ $t('demo.count') }}</p>
    <img :src="ICON_LOGO" class="logo" alt="logo">
    <HelloWorld :msg="msg" version="v1.2.1" />
    <ElButton @click="toggleLocale">
      {{ toggleLocaleText }}
    </ElButton>
    <el-time-picker
      v-model="value1"
      is-range
      range-separator="To"
      start-placeholder="Start time"
      end-placeholder="End time"
    />
    <div>Element Plus 图标使用</div>
    <i-ep:apple />
    <i-ep-delete class="h-20 w-20" />
    <i-ep:circle-plus class="text-2xl text-red-500" />
    <div>本地图标使用</div>
    <i-svg:home class="inline h-10 w-10" />
    <i-svg:icon-park-solid-apple class="inline text-size-20 text-red-6" />
  </div>
  <div>
    <vxe-table :data="tableData" :empty-text="$t('result.empty')">
      <vxe-column type="seq" width="70" />
      <vxe-column field="name" title="Name" />
      <vxe-column field="sex" title="Sex" />
      <vxe-column field="age" title="Age" />
    </vxe-table>
  </div>
</template>

<style scoped>
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}

.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
