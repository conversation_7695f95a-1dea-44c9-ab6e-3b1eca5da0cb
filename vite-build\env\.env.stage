# 预发布环境

## 项目名称
VITE_APP_NAME = Recircle
## 项目描述
VITE_APP_DESC =
## 打包路径（就是网站前缀，例如部署到 https://xxx.github.io/simple-apple/ 域名下，就需要填写 /simple-apple/）
VITE_BASE_URL = /
## API地址，优先顺序：_app.global.js > env
VITE_BASE_API = http://pre-iiotplatform.irecircle.com/tenant-api
## OSS地址，优先顺序：_app.global.js > env
VITE_OSS_API = http://pre-tenant.irecircle.com/common-api/infra/file/upload
## 租户跳转地址
VITE_TENANT_REDIRECT_URL = http://pre-tenant.irecircle.com/workbench
## 路由模式
## - "history": 历史模式
## - "hash": 哈希模式
VITE_ROUTE_MODE = history
## 构建压缩类型
## - "gzip": gz
## - "brotli": br
## - "gzip,brotli": gz+br
## - "none": 不压缩
VITE_BUILD_COMPRESS = gzip
## 是否删除打印信息
## - "Y"/"true": true
## - "N"/"false": false
VITE_DROP_CONSOLE = Y

