variables:
  TZ: Asia/Shanghai # 设置时区为上海时间
  DOCKER_REGISTRY_PASS: Harbor12345
  DOCKER_REGISTRY: harbor.irecircle.com
  DOCKER_REGISTRY_USER: admin
  IMAGE_NAME: recircle-website

before_script:
  - source ~/.bashrc

stages:
  - install
  - build
  - build_img
  - deploy

include:
  - local: .gitlab-ci/develop.yml
  - local: .gitlab-ci/test.yml
  - local: .gitlab-ci/release.yml
  - local: .gitlab-ci/prod.yml
