import SkTipDialog, { type Prop as TipDialogProp } from '@/components/TipDialog/SkTipDialog.vue'
import { createApp } from 'vue'

type OnCancelFunc = () => void
type OnConfirmFunc = (done: () => void, close: () => void) => void

export default (props: TipDialogProp & { onCancel?: OnCancelFunc, onConfirm?: OnConfirmFunc }) => {
  const mountNode = document.createElement('div')
  const instance = createApp(SkTipDialog, {
    ...props,
    modelValue: true,
    onCancel: () => {
      instance.unmount()
      mountNode.remove()
    },
  })

  const appElement = document.querySelector((props.dialogAttrs?.appendTo as string) || '.sk-section')
  appElement?.appendChild(mountNode)
  // document.body.appendChild(mountNode)
  instance.mount(mountNode)

  return instance
}
