<script setup lang="ts">
import { getRealNameAuthentication } from '@/api/personal/real-name-authentication'
import { GENDER_OPTIONS, REAL_NAME_STATUS, STATUS_OPTIONS } from '@/constant'
import { CertificateTypeEnum, CertificateTypeEnumText } from '@/enum'
import dayjs from 'dayjs'

defineOptions({
  name: 'PersonalDetails',
})
const { userInfo } = useUserStore()
const dataDetail = ref<any>({})
const activeName = ref('basics')
async function getAccountInfoApi() {
  dataDetail.value = await getRealNameAuthentication(userInfo!.id as string)
}
onBeforeMount(() => {
  getAccountInfoApi()
})
</script>

<template>
  <div class="basic-setting personal-page">
    <el-descriptions title="实名认证" :column="2">
      <el-descriptions-item label="实名状态">
        {{ REAL_NAME_STATUS.find(item => item.value === dataDetail.realNameStatus)?.label }}
      </el-descriptions-item>
      <el-descriptions-item label="用户账号ID">
        {{ dataDetail.id }}
      </el-descriptions-item>
      <el-descriptions-item label="注册时间">
        {{ dayjs(dataDetail.createTime).format('YYYY-MM-DD HH:mm:ss') }}
      </el-descriptions-item>
      <el-descriptions-item label="用户账号状态">
        {{ STATUS_OPTIONS.find(item => item.value === dataDetail.status)?.label }}
      </el-descriptions-item>
      <el-descriptions-item label="最后登录时间">
        {{ dayjs(dataDetail.loginDate).format('YYYY-MM-DD HH:mm:ss') }}
      </el-descriptions-item>
    </el-descriptions>
    <div class="h-15px w-[calc(100%+60px)] bg-[#f5f7f9] -ml-7.5" />
    <template v-if="dataDetail.realNameAuditLogRespVO">
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane label="实名信息" name="basics" />
      </el-tabs>
      <el-descriptions title="" :column="2">
        <el-descriptions-item label="实名姓名">
          {{ dataDetail.realNameAuditLogRespVO?.name }}
        </el-descriptions-item>
        <el-descriptions-item label="性别">
          {{ GENDER_OPTIONS.find(item => item.value === dataDetail.realNameAuditLogRespVO?.sex)?.label }}
        </el-descriptions-item>
        <el-descriptions-item label="拼音">
          {{ dataDetail.realNameAuditLogRespVO?.nameSpelling }}
        </el-descriptions-item>
        <el-descriptions-item label="证件类型">
          {{ [
            {
              label: CertificateTypeEnumText[CertificateTypeEnum.ID_CARD],
              value: CertificateTypeEnum.ID_CARD,
            },
          ].find(item => item.value === dataDetail.realNameAuditLogRespVO?.idType)?.label }}
        </el-descriptions-item>
        <el-descriptions-item label="证件号码">
          {{ dataDetail.realNameAuditLogRespVO?.idNumber }}
        </el-descriptions-item>
        <el-descriptions-item label="年龄">
          {{ dataDetail.realNameAuditLogRespVO?.age }}
        </el-descriptions-item>
        <el-descriptions-item label="手机号码">
          {{ dataDetail.realNameAuditLogRespVO?.mobile }}
        </el-descriptions-item>
      </el-descriptions>
    </template>
    <el-empty v-else description="您的实名认证信息已提交，请耐心等候" />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-descriptions__label) {
  color: #a5a5a5;
}

.text-custom-color {
  color: #777777;
}

.background-color {
  background-color: #5180f3;
  border-radius: 5px;
  color: #fff;
  line-height: 36px;
  text-align: center;
}
</style>
