import type { App } from 'vue'
import type {
  RouteLocationNormalized,
  RouteLocationNormalizedLoaded,
  Router,
  RouteRecordRaw,
} from 'vue-router'
import { $t } from '@/plugins/i18n'
import { createRouter, createWebHistory } from 'vue-router'

import { routerPermission } from './permission'

// 自动导入modules目录下的路由
const moduleRoutes: RouteRecordRaw[] = []
const allModules: { [k: string]: any } = import.meta.glob('./modules/*.ts', { eager: true })
Object.keys(allModules).forEach((name) => {
  if (name.includes('_'))
    return
  moduleRoutes.push(...allModules[name].default)
})

export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('@/layout/Base.vue'),
    redirect: {
      name: 'Personal',
    },
    children: [
      {
        path: '/personal',
        name: 'Personal',
        component: () => import('@/layout/personal-layout/index.vue'),
        redirect: { name: 'UserCenter' },
        meta: {
          title: $t('router.personal'),
          auth: false,
        },
        children: [
          {
            path: 'user-center',
            name: 'UserCenter',
            component: () => import('@/views/personal/user-center/index.vue'),
            meta: {
              title: $t('router.userCenter'),
              auth: true,
            },
          },
          {
            path: 'basic-setting',
            name: 'BasicSetting',
            component: () => import('@/views/personal/basic-setting/index.vue'),
            meta: {
              title: $t('router.base'),
              auth: true,
            },
          },
          {
            path: 'safe-setting',
            name: 'SafeSetting',
            component: () => import('@/views/personal/safe-setting/index.vue'),
            meta: {
              title: $t('router.security'),
              auth: true,
            },
          },
          {
            path: 'notification-setting',
            name: 'NotificationSetting',
            component: () => import('@/views/personal/notification-setting/index.vue'),
            meta: {
              title: $t('router.notification'),
              auth: true,
            },
          },
          {
            path: 'real-name-authentication',
            name: 'RealNameAuthentication',
            component: () => import('@/views/personal/real-name-authentication/index.vue'),
            meta: {
              title: $t('router.authentication'),
              auth: true,
            },
          },
          {
            path: 'enterprise-details',
            name: 'EnterpriseDetails',
            component: () => import('@/views/personal/real-name-authentication/enterprise-details.vue'),
            meta: {
              title: $t('router.enterpriseDetails'),
              auth: true,
            },
          },
          {
            path: 'personal-details',
            name: 'PersonalDetails',
            component: () => import('@/views/personal/real-name-authentication/personal-details.vue'),
            meta: {
              title: $t('router.personalDetails'),
              auth: true,
            },
          },
          {
            path: 'enterprise-authentication',
            name: 'EnterpriseAuthentication',
            component: () => import('@/views/personal/real-name-authentication/enterprise-authentication.vue'),
            meta: {
              title: $t('router.enterpriseAuthentication'),
              auth: true,
            },
          },
          {
            path: 'personal-authentication',
            name: 'PersonalAuthentication',
            component: () => import('@/views/personal/real-name-authentication/personal-authentication.vue'),
            meta: {
              title: $t('router.personalAuthentication'),
              auth: true,
            },
          },
        ],
      },
    ],
  },
  {
    path: '/403',
    component: () => import('@/views/error/403.vue'),
    name: '403',
    meta: {
      title: '403',
    },
  },
  {
    path: '/404',
    component: () => import('@/views/error/404.vue'),
    name: '404',
    meta: {
      title: '404',
    },
  },
  {
    path: '/500',
    component: () => import('@/views/error/500.vue'),
    name: '500',
    meta: {
      title: '500',
    },
  },
  ...moduleRoutes,
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404',
  },
]

export const router: Router = createRouter({
  routes,
  history: createWebHistory(),
  strict: true,
  scrollBehavior(
    to: RouteLocationNormalized,
    _from: RouteLocationNormalizedLoaded,
    savedPosition,
  ) {
    if (savedPosition) {
      return savedPosition
    } else if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth',
      }
    } else {
      return { top: 0 }
    }
  },
})

export async function setupRouter(app: App<Element>) {
  app.use(router)
  routerPermission(router)
  await router.isReady()
}
