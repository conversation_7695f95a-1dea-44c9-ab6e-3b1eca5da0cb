/**
 * 应用支持的语言环境枚举
 */
export enum LocaleEnum {
  // 简体中文
  ZH_CN = 'zh-cn',
  // 英语（美国）
  EN_US = 'en',
}

/**
 * 存储相关的Key枚举
 */
export enum StorageKeyEnum {
  // 用户信息
  USER_INFO = '__USER_INFO__',
  // 用户Token
  USER_TOKEN = '__USER_TOKEN__',
}

/**
 * 登录页面的小组件类型枚举
 */
export enum LoginTabEnum {
  SIGN_IN = 'signIn',
  SIGN_UP = 'signUp',
  FORGOT_PASSWORD = 'forgotPassword',
}

/**
 * 登录方式
 */
export enum LoginTypeEnum {
  PASSWORD = 'password',
  MOBILE = 'mobile',
}
