import type { FormInstance } from 'element-plus'
import { sendCodeByLogin } from '@/api/user'
import { cryptoJS, PHONE_REG } from '@2030/utils'
import { clone } from 'xe-utils'

export function useSignIn(
  passwordFormRef: Ref<FormInstance>,
  mobileFormRef: Ref<FormInstance>,
) {
  /**
   * 密码登录
   */
  const passwordFormParams = reactive({
    username: '',
    password: '',
  })

  const { loginUsePassword, loginUsePhone } = useUserStore()
  const { btnLoading } = useBtnLoading()
  async function loginPassword() {
    if (!passwordFormRef.value)
      return
    const validated = await passwordFormRef.value.validate()
    if (!validated)
      return
    btnLoading.value = true
    const clonePasswordFormParams = { ...clone(passwordFormParams, true), password: cryptoJS.encryptMD5(passwordFormParams.password) }
    loginUsePassword(clonePasswordFormParams)
  }

  /**
   * 手机登录
   */
  const mobileFormParams = reactive({
    mobile: '',
    captchaCode: '',
  })

  async function loginPhone() {
    if (!mobileFormRef.value)
      return
    const validated = await mobileFormRef.value.validate()
    if (!validated)
      return
    btnLoading.value = true
    loginUsePhone(mobileFormParams)
  }
  const { countdown, isCounting, handleSendCode } = useCountDown()
  const verifyBtnDisabled = computed(() => isCounting.value || !PHONE_REG.test(mobileFormParams.mobile))
  async function handleGetCode() {
    const status = await sendCodeByLogin({ mobile: mobileFormParams.mobile })
    if (status) {
      handleSendCode()
    }
  }

  return {
    passwordFormParams,
    mobileFormParams,
    verifyBtnDisabled,
    countdown,
    isCounting,
    btnLoading,
    loginPassword,
    loginPhone,
    handleGetCode,
  }
}
