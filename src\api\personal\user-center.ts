import type * as Types from './types'
import { getToken } from '@/service/auth'
import request from '@/service/axios'

/**
 * @file 进入租户-选择租户相关API
 */

/** 查询用户加入的租户列表 */
export const getUserTenantList = (userId: string) => request.get<Types.TenantListRes>({ url: `/user-center/user-tenant/queryUserTenantList?userId=${userId}` })

/**
 * 切换租户获取租户Token
 * ? 为什么header中有token,请求参数中又携带token?
 * 后端接口需要重新解析Token，并校验Token是否合法，从而换取新的Token -by 胡飞飞
 */
export const getTenantToken = (tenantId: string) => request.get({ url: `/tenant/base/token/${tenantId}?token=${getToken()}` })
