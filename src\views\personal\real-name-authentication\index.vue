<script setup lang="ts">
import { getMyEnterpriseDetails, getRealNameAuthentication } from '@/api/personal/real-name-authentication'
import { REAL_NAME_STATUS_ENUM } from '@/enum'

defineOptions({
  name: 'RealNameAuthentication',
})

const router = useRouter()
const { userInfo } = useUserStore()
const dataDetail = ref<any>({})
async function goCertification(type: number) {
  if (type === 1) {
    if (dataDetail.value.realNameStatus === 0) {
      router.push({ name: 'PersonalAuthentication' })
    } else {
      router.push({ name: 'PersonalDetails' })
    }
  } else {
    if (dataDetail.value.realNameStatus === 1) {
      const res = await getMyEnterpriseDetails() as { auditStatus: number }
      if (res && res.auditStatus !== 2) {
        router.push({ name: 'EnterpriseDetails' })
      } else {
        router.push({ name: 'EnterpriseAuthentication' })
      }
    } else {
      $message.warning('请先做完【个人认证】后进行企业认证')
    }
  }
}
async function getAccountInfoApi() {
  dataDetail.value = await getRealNameAuthentication(userInfo!.id as string)
}
onBeforeMount(() => {
  getAccountInfoApi()
})

const personalText = computed(() => {
  if (dataDetail.value.realNameStatus === REAL_NAME_STATUS_ENUM.VERIFIED) {
    return '查看'
  } else {
    return '去认证'
  }
})
</script>

<template>
  <div class="personal-page h-52.5">
    <div class="mt-5 w-250 flex items-center justify-between -ml-7.5">
      <div class="PERSON h-42.5 w-125">
        <div pl-78.25 pt-4.75>
          <p mb-2.5 text-xl font-bold>
            个人认证
          </p>
          <p class="text-custom-color" text-sm font-normal>
            实名后开启更多权益
          </p>
          <div mt-8.5 h-9 w-20.75 cursor-pointer class="background-color" @click="goCertification(1)">
            {{ personalText }}
          </div>
        </div>
      </div>
      <div class="ENTERPRISE h-42.5 w-125">
        <div pl-78.25 pt-4.75>
          <p mb-2.5 text-xl font-bold>
            企业认证
          </p>
          <p class="text-custom-color" text-sm font-normal>
            实名后开启更多权益
          </p>
          <div mt-8.5 h-9 w-20.75 cursor-pointer class="background-color" @click="goCertification(2)">
            去认证
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-descriptions__label) {
  color: #a5a5a5;
}

.PERSON {
  background-image: url('@/assets/imgs/person-certification.png');
}

.ENTERPRISE {
  background-image: url('@/assets/imgs/enterprise-certification.png');
}

.text-custom-color {
  color: #777777;
}

.background-color {
  background-color: #5180f3;
  border-radius: 5px;
  color: #fff;
  line-height: 36px;
  text-align: center;
}
</style>
