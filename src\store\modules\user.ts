import type { AccountBaseInfoRes } from '@/api/personal/base-setting'
import type * as Login from '@/api/user/types'
import { getAccountInfo } from '@/api/personal/base-setting'
import { loginByMobile, loginByPassword, postLogout } from '@/api/user'
import { removeToken, setToken } from '@/service/auth'
import { piniaPersistConfig } from '@/store/helper'

export interface UserState {
  loginUsePassword: (params: { username: string, password: string }) => Promise<void>
  loginUsePhone: (params: { mobile: string, captchaCode: string }) => Promise<void>
  logout: () => Promise<void>
  autoLogin: Ref<boolean>
  setAutoLogin: (value: boolean) => void
  userInfo: Ref<AccountBaseInfoRes | undefined>
  setUserInfo: (value: AccountBaseInfoRes) => void
  updateUserInfo: (value: Recordable) => void
}

export const useUserStore = defineStore('user', (): UserState => {
  const router = useRouter()
  const route = useRoute()

  /**
   * 登录后处理
   * @param loginRes 登录响应数据
   */
  const userInfo = ref<AccountBaseInfoRes>()
  const { btnLoading } = useBtnLoading()
  const handleAfterLogin = async (loginRes: Login.LoginResData) => {
    try {
      await setToken(loginRes.accessToken)
      const res = await getAccountInfo()
      userInfo.value = res
      router.replace({ path: route.query.redirect as string || '/' })
    } finally {
      btnLoading.value = false
    }
  }

  /**
   * 账号密码登录
   * @param data 账号密码登录请求参数
   */
  const loginUsePassword = async (data: Login.LoginPasswordReqData) => {
    try {
      const loginRes = await loginByPassword(data)
      handleAfterLogin(loginRes)
    } catch {
      btnLoading.value = false
    }
  }

  /**
   * 手机号登录
   * @param data 手机号登录请求参数
   */
  const loginUsePhone = async (data: Login.LoginMobileReqData) => {
    try {
      const loginRes = await loginByMobile(data)
      handleAfterLogin(loginRes)
    } catch {
      btnLoading.value = false
    }
  }

  /**
   * 设置自动登录
   * @param value 是否自动登录
   */
  const autoLogin = ref(false)
  const setAutoLogin = (value: boolean) => {
    autoLogin.value = value
  }

  /**
   * 更新用户信息
   * @param value 用户信息
   */
  const setUserInfo = (value: AccountBaseInfoRes) => {
    userInfo.value = value
  }

  const updateUserInfo = (model: Recordable) => {
    Object.assign(userInfo.value as AccountBaseInfoRes, model)
  }

  /**
   * 登出
   */
  const logout = async () => {
    await postLogout()
    await removeToken()
    await localStorage.removeItem('user')
    router.replace({ name: 'Login' })
  }

  return { loginUsePassword, loginUsePhone, logout, setAutoLogin, setUserInfo, autoLogin, userInfo, updateUserInfo }
}, { persist: piniaPersistConfig('user') })

/**
 * 获取用户store，在 setup 外使用
 * @returns 用户store
 */
export function useUserStoreHook() {
  return useUserStore()
}
