{"name": "recircle-website", "type": "module", "version": "0.0.1", "private": true, "packageManager": "pnpm@10.4.1+sha512.c753b6c3ad7afa13af388fa6d808035a008e30ea9993f58c6663e2bc5ff21679aa834db094987129aa4d488b86df57f7b634981b2f827cdcacc698cc0cfb88af", "author": "<PERSON><<EMAIL>>", "license": "MIT", "engines": {"node": ">=18 || >=20"}, "scripts": {"local": "vite --mode env.local", "dev": "vite --mode env.dev", "build:dev": "vue-tsc --noEmit && vite build --mode dev", "build:test": "vue-tsc --noEmit && vite build --mode test", "build:stage": "vue-tsc --noEmit && vite build --mode stage", "build:prod": "vue-tsc --noEmit && vite build --mode prod", "preview": "vite preview", "document": "cd docs && pnpm docs:dev", "lint": "eslint . --fix", "lint-staged": "lint-staged", "prepare": "simple-git-hooks", "release": "release-it", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "dependencies": {"@2030/utils": "workspace:*", "@element-plus/icons-vue": "2.3.1", "@vueuse/core": "11.2.0", "axios": "1.7.7", "axios-retry": "4.5.0", "dayjs": "1.11.13", "element-plus": "2.8.7", "nprogress": "0.2.0", "pinia": "2.2.6", "pinia-plugin-persistedstate": "4.1.2", "vue": "3.5.12", "vue-i18n": "10.0.4", "vue-json-pretty": "2.4.0", "vue-router": "4.4.5", "vxe-table": "4.6.23", "xe-utils": "3.5.31"}, "devDependencies": {"@2030/commitlint-config": "2.0.2", "@2030/eslint-config": "2.0.3", "@commitlint/cli": "19.5.0", "@iconify-json/ep": "1.2.1", "@intlify/unplugin-vue-i18n": "5.2.0", "@release-it/conventional-changelog": "9.0.2", "@types/nprogress": "0.2.3", "@unocss/eslint-plugin": "0.63.6", "@vitejs/plugin-vue": "5.1.4", "@vitejs/plugin-vue-jsx": "4.0.1", "eslint": "9.14.0", "eslint-plugin-format": "0.1.2", "lint-staged": "15.2.10", "release-it": "17.10.0", "sass": "1.69.5", "simple-git-hooks": "2.11.1", "typescript": "5.5.4", "unocss": "0.63.6", "unplugin-auto-import": "0.18.3", "unplugin-icons": "0.20.0", "unplugin-vue-components": "0.27.4", "vite": "5.4.10", "vite-plugin-compression": "0.5.1", "vite-plugin-html": "3.2.2", "vite-plugin-vue-devtools": "7.6.2", "vue-tsc": "2.1.10"}, "simple-git-hooks": {"commit-msg": "pnpm commitlint --edit $1", "pre-commit": "pnpm typecheck && pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}