export function useCountDown(time = 60) {
  /**
   * 添加倒计时相关的响应式变量
   */
  const countdown = ref<number>(time)
  const isCounting = ref<boolean>(false)
  let timer: ReturnType<typeof setInterval> | null = null

  /**
   * 发送验证码的方法
   */
  function handleSendCode() {
    if (isCounting.value)
      return
    isCounting.value = true

    timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer!)
        countdown.value = time
        isCounting.value = false
      }
    }, 1000)
  }

  /**
   * 组件卸载时清除定时器
   */
  onBeforeUnmount(() => {
    if (timer) {
      clearInterval(timer)
    }
  })

  return { countdown, isCounting, handleSendCode }
}
