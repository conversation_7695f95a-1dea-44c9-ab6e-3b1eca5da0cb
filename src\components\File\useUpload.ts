import type { ElUpload, UploadFile, UploadFiles, UploadInstance, UploadProps, UploadRawFile, UploadUserFile } from 'element-plus'
import type { Ref } from 'vue'
import { getToken } from '@/service/auth'
import { ElMessage, genFileId } from 'element-plus'
import { computed, ref, useTemplateRef, watch } from 'vue'

export interface FileInfo {
  documentNo: string
  documentName: string
  documentVersion: string | null
  documentLength: number
  documentUrl: string
  uploadTime: number
}
export type ElUploadRef = InstanceType<typeof ElUpload>

export interface FileUploaderProps {
  /** 上传提示信息--不传 SkImageUpload.vue中默认为 最多上传10个文件，单个文件10MB以内，格式支持jpg/png，SkFileUploader.vue中默认为 最多上传10个文件，单个文件10MB以内，格式支持jpg/png/pdf/docx/xlsx/ppt/txt/zip/rar等 */
  tip?: string
  /** 是否自动提示--不传默认为false。当为true时，tip提示会根据limit、isLimitSize、limitSize条件进行替换默认tip中对应字段的内容 */
  isAutoTip?: boolean
  /** 是否显示tip--不传默认为true */
  isShowTip?: boolean
  /** 是否支持多选文件--不传默认为true */
  multiple?: boolean
  /** 接受上传的文件类型--不传 SkImageUpload.vue中默认为 .jpg,.jpeg,.png，SkFileUploader.vue中默认为 .jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar,.gz,.bz2,.7z,.tar,.tgz,.tar.gz,.tar.bz2, .gif */
  accept?: string
  /** 是否限制文件类型--不传默认为true */
  isAccept?: boolean
  /** 上传文件类型不符合提示语--不传默认为 请上传符合格式的文件类型 */
  acceptMsg?: string
  /** 限制上传文件数量--不传默认为10 */
  limit?: number
  /** 是否限制文件大小--不传默认为true */
  isLimitSize?: boolean
  /** 限制上传文件大小(单位：MB)--不传默认为10MB */
  limitSize?: number
  /** 上传文件大小不符合提示语--不传默认为 请上传符合大小的文件 */
  limitMsg?: string
  /** 上传地址--不传默认为空，需传完整地址 */
  uploadUrl?: string
  /** el-upload的attrs */
  uploadAttrs?: ElUploadRef['$props'] & Recordable
}
interface Options {
  /** 是否限制文件类型 */
  isAccept?: boolean
  /** 接受上传的文件类型 */
  accept?: string
  /** 上传文件类型不符合提示语 */
  acceptMsg?: string
  /** 是否限制文件大小 */
  isLimitSize?: boolean
  /** 限制上传文件大小 */
  limitSize?: number
  /** 上传文件大小不符合提示语 */
  limitMsg?: string
}

/**
 * 上传文件前的处理函数
 * @param file 要上传的文件
 * @param options 上传文件的选项
 * @returns 是否允许上传
 */
export function useBeforeUpload(file: UploadFile | UploadRawFile, options?: Options) {
  const { isAccept, accept, acceptMsg, isLimitSize, limitSize, limitMsg } = options || {}

  // 文件格式
  const fileReg = file.name.replace(/.+\./, '')

  if (isAccept && accept && !accept.includes(fileReg.toLowerCase())) {
    ElMessage.warning(`当前文件"${file.name}"的文件类型为${fileReg}，${acceptMsg}`)
    return false
  }

  // 大小
  const size = (file.size ?? 0) / 1024 / 1024
  if (isLimitSize && limitSize && size > limitSize) {
    ElMessage.warning(`当前文件"${file.name}"的文件大小为${size.toFixed(2)}MB，${limitMsg}`)
    return false
  }

  return true
}

export function useUpload(fileType: 'image' | 'file', props: FileUploaderProps, modelFiles: Ref<FileInfo[]>) {
  const { tip, isAutoTip, limit, accept, isAccept, acceptMsg, isLimitSize, limitSize, limitMsg, uploadUrl } = props
  const fileConfig = { accept, isAccept, acceptMsg, isLimitSize, limitSize, limitMsg }

  const uploadRef = useTemplateRef<UploadInstance>('uploadRef')
  const fileList = ref<UploadUserFile[]>([])

  const getTip = computed(() => {
    if (!isAutoTip)
      return tip
    const limitTip = limit ? `最多上传${limit}${fileType === 'file' ? '个附件，' : '张图片，'}` : ''
    const sizeTip = isLimitSize ? `单个文件${limitSize}MB以内，` : ''
    const acceptTip = fileType === 'file' ? '格式支持jpg/png/pdf/docx/xlsx/ppt/txt/zip/rar等' : '格式支持jpg/png'
    return limitTip + sizeTip + acceptTip
  })

  const previewUrl = ref('')
  /** 图片预览 */
  const handlePreview: UploadProps['onPreview'] = (uploadFile) => {
    previewUrl.value = uploadFile.url!
  }
  const removeFile = (uploadFiles: UploadFiles | UploadUserFile[] | FileInfo[], index: number) => uploadFiles.splice(index, 1)

  const uploadApi = (options: { isExceed: boolean, uploadRawFile?: UploadRawFile, uploadFiles: UploadFiles | UploadUserFile[], index: number }) => {
    const { isExceed, uploadRawFile, uploadFiles, index } = options
    const formData = new FormData()
    formData.append('file', uploadRawFile!)
    fetch(uploadUrl!, {
      method: 'POST',
      body: formData,
      headers: {
        authorization: getToken(),
      } as any,
    }).then(async (res) => {
      try {
        const dataRes = (await res.json()) as ResData<FileInfo>
        if (dataRes.code === 0) {
          uploadFiles[index].url = dataRes.data.documentUrl
          uploadFiles[index].status = 'success'
          fileList.value = [...uploadFiles]

          if (isExceed)
            modelFiles.value = [dataRes.data]
          else modelFiles.value = [...modelFiles.value, dataRes.data]

          ElMessage.success(dataRes.msg || '上传成功')
        } else {
          removeFile(uploadFiles, index)
          ElMessage.error(dataRes.msg || '上传失败')
        }
      } catch (error) {
        removeFile(uploadFiles, index)
        ElMessage.error('系统错误')
        console.error('Error in upload handling:', error)
      }
    })
  }
  const handleChange: UploadProps['onChange'] = (uploadFile, uploadFiles) => {
    const index = uploadFiles.findIndex(item => item.url === uploadFile.url)

    if (!useBeforeUpload(uploadFile, fileConfig)) {
      removeFile(uploadFiles, index)
    } else {
      uploadApi({ isExceed: false, uploadRawFile: uploadFile.raw, uploadFiles, index })
    }
  }

  /** 图片删除 */
  const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {
    fileList.value = [...uploadFiles]
    const index = modelFiles.value.findIndex(item => item.documentUrl === uploadFile.url)
    removeFile(modelFiles.value, index)
  }

  /** 附件删除 */
  const handleRemoveFile = (uploadFile: Recordable) => {
    const index = fileList.value.findIndex(item => item.url === uploadFile.url)
    removeFile(fileList.value, index)
    removeFile(modelFiles.value, index)
  }

  // 达到limit限制
  const handleExceed: UploadProps['onExceed'] = (files) => {
    const { limit } = props
    const file = files[0] as UploadRawFile
    if (limit === 1 && useBeforeUpload(file, fileConfig)) {
      uploadRef.value?.clearFiles()
      file.uid = genFileId()

      const { name, size, uid } = file
      const newUploadFile: UploadUserFile = {
        name,
        percentage: 0,
        raw: file,
        size,
        status: 'ready',
        uid,
        url: '',
      }
      uploadApi({ isExceed: true, uploadRawFile: file, uploadFiles: [newUploadFile], index: 0 })
    } else if (limit && limit > 1) {
      ElMessage.warning(`最大支持上传${limit}${fileType === 'file' ? '个附件' : '张图片'}`)
    }
  }

  // const initFileList = (files: Recordable[]) => {
  //   for (const item of files) {
  //     item.name = extractFilePath(item.url)
  //     item.status = 'success'
  //     item.uid = genFileId()
  //   }
  //   fileList.value = files as UploadUserFile[]
  // }

  watch(
    () => modelFiles.value,
    (newVal) => {
      fileList.value = newVal.map(item => ({ name: item.documentName, url: item.documentUrl, status: 'success', size: item.documentLength, uid: genFileId() }))
    },
    { deep: true, immediate: true },
  )

  return {
    uploadRef,
    fileList,
    getTip,
    previewUrl,
    handlePreview,
    handleChange,
    handleRemove,
    handleRemoveFile,
    handleExceed,
  }
}
