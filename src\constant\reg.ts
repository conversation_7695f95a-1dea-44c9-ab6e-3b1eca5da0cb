/**
 * 常用正则
 *
 * NOTE: 推荐正则表达式写入至 `packages/utils/src/reg` 目录下
 */

export const REG = {
  /**
   * 匹配中国大陆手机号码 - 11位数字
   * @example 13800138000, 15912345678, 18912345678
   */
  PHONE: /^1[3-9]\d{9}$/,

  /**
   * 匹配常见的电子邮箱格式
   * @example <EMAIL>, <EMAIL>, <EMAIL>
   */
  EMAIL: /^[\w-]+@[\w-]+(\.[\w-]+)+$/,

  /**
   * 匹配18位身份证号码
   * @example 11010519491231890X, 11010519491231890X
   */
  ID_CARD: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[\dX]$/i,

  /**
   * 是否含有字母
   * @example abcdefg54, ABCDEFG526
   */
  HAS_LETTER: /[a-z]/i,

  /**
   * 是否含有数字
   * @example 12345fag6, 987654aga3210
   */
  HAS_NUMBER: /\d/,

  /**
   * 是否含有特殊字符
   * @example 345fa!@#$%^&*(),.?":{}|<>
   */
  HAS_SPECIAL_CHAR: /[\s!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/,

  /**
   * 匹配数字、英文大小写，下划线
   * @example 12345fag6, abcdefg54, ABCDEFG52_6
   */
  NUM_ENG_SYM: /^\w+$/,
  /**
   * 匹配中文、数字、英文大小写
   */
  CN_NUM_ENG_REGEXP: /^[\u4E00-\u9FA5a-z0-9]+$/i,
  /** 匹配中文正则 */
  CHINESE_REGEXP: /^[\u4E00-\u9FFF]+$/,
  /** 匹配数字正则 */
  NUMBER_REGEXP: /^\d*$/,
}
