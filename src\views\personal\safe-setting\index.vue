<script setup lang="ts">
import { getSafeSetting, type SafeInfo } from '@/api/personal/safe-setting'
import AccountPassword from './components/AccountPassword.vue'
import BackupEmail from './components/BackupEmail.vue'
import ConfidentialPhone from './components/ConfidentialPhone.vue'
import ConfidentialQuestion from './components/ConfidentialQuestion.vue'
import MFADevice from './components/MFADevice.vue'

defineOptions({
  name: 'SafeSetting',
})

const safeInfo: SafeInfo = reactive({
  passwordStrength: 3,
  desensitizationMobile: '',
  desensitizationEmail: '',
})

async function getSafeSettingApi() {
  const data = await getSafeSetting()
  const keys = Reflect.ownKeys(data) as (keyof SafeInfo)[]
  for (const key of keys) {
    // eslint-disable-next-line ts/ban-ts-comment
    // @ts-expect-error
    safeInfo[key] = data[key]
  }
}

onBeforeMount(() => {
  getSafeSettingApi()
})
</script>

<template>
  <div class="personal-page safe-setting">
    <span class="personal-page-title">安全设置</span>
    <AccountPassword :password-strength="safeInfo.passwordStrength" />
    <el-divider />
    <ConfidentialPhone :mobile="safeInfo.desensitizationMobile" />
    <el-divider />
    <ConfidentialQuestion />
    <el-divider />
    <BackupEmail :spare-email="safeInfo.desensitizationEmail" />
    <el-divider />
    <MFADevice />
  </div>
</template>

<style lang="scss" scoped>
.safe-setting {
  > div {
    display: flex;
    justify-content: space-between;
    align-items: center;

    :deep(.content) {
      font-size: 14px;
      display: flex;
      flex-direction: column;

      .title {
        font-weight: 700;
        margin-bottom: 10px;
      }

      .info {
        color: var(--sk-text);
      }
    }
  }

  :deep(.el-divider) {
    margin: 20px 0 30px;
    border-color: var(--sk-E4);
  }
}
</style>
