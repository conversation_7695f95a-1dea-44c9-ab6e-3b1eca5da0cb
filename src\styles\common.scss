/* personal 个人设置模块路由页面的样式 */
.personal-page {
  width: 1000px;
  padding: 20px 30px;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 5px;

  &-title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 30px;
    display: block;
  }
}

/* 小标题左侧before元素样式 */
.small-title-before {
  content: '';
  width: 5px;
  height: 16px;
  background: var(--el-color-primary);
  margin-right: 10px;
  border-radius: 4px;
}

/* el-dialog 对话框样式 */
.el-dialog {
  width: 950px;
  box-sizing: border-box;
  padding: 0;
  border-radius: 5px;

  .el-dialog__header {
    height: 50px;
    box-sizing: border-box;
    padding: 0 20px;
    border-radius: 5px 5px 0 0;
    background-color: #f3f3f3;
    display: flex;
    align-items: center;

    .el-dialog__title {
      font-size: 16px;
      color: #333333;
      font-weight: 700;
      display: flex;
      align-items: center;
    }

    .el-dialog__title::before {
      @extend .small-title-before;
    }

    .el-dialog-not-base.el-dialog__title::before {
      width: 0;
      margin-right: 0;
    }

    .el-dialog__close {
      font-size: 20px;
    }
  }

  .el-dialog__footer {
    padding: 20px;
    height: 80px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;

    .el-button {
      min-width: 140px;
      height: 40px;
      border-radius: 24px;
      border: 1px solid var(--sk-E4);
      color: var(--sk-text-deep);
      font-size: 18px;
    }

    .el-button:hover {
      border-color: var(--el-color-primary);
      color: var(--el-color-primary);
      background-color: #fff;
    }
  }
}

// el-message组件样式
.el-message {
  .el-message__content {
    word-break: break-all;
  }
}
