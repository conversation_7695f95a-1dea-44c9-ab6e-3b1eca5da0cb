<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const personalMenus = [
  { title: '用户中心', name: 'UserCenter' },
  { title: '基本设置', name: 'BasicSetting' },
  { title: '安全设置', name: 'SafeSetting' },
  { title: '通知设置', name: 'NotificationSetting' },
  { title: '实名认证', name: 'RealNameAuthentication' },
]

function handlePersonalMenu(name: string) {
  router.push({ name })
}
</script>

<template>
  <div class="personal-menu">
    <ul class="menu-box">
      <li
        v-for="item in personalMenus"
        :key="item.name"
        :class="{ 'is-active': router.currentRoute.value.name === item.name }"
        @click="handlePersonalMenu(item.name)"
      >
        {{ item.title }}
      </li>
    </ul>
  </div>
</template>

<style lang="scss" scoped>
.personal-menu {
  margin-right: 15px;

  .menu-box {
    width: 185px;
    padding: 10px 0;
    background-color: #fff;
    border-radius: 5px;
    list-style: none;
    margin: 0;

    li {
      padding: 10px 22px;
      cursor: pointer;
      font-size: 14px;
    }

    .is-active {
      position: relative;
      color: var(--el-color-primary);
    }

    .is-active::before {
      content: '';
      width: 2px;
      height: 19px;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      background-color: var(--el-color-primary);
    }
  }
}
</style>
