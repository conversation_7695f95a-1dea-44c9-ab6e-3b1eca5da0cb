<script setup lang="ts">
import PersonalMenu from './PersonalMenu.vue'

defineOptions({
  name: 'PersonalLayout',
})
</script>

<template>
  <div class="personal-layout">
    <ElScrollbar
      native
      always
    >
      <div class="personal-content">
        <PersonalMenu />
        <router-view />
      </div>
    </ElScrollbar>
  </div>
</template>

<style lang="scss" scoped>
.personal-layout {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  background-color: var(--sk-background);

  .personal-content {
    width: 1230px;
    display: flex;
    justify-content: center;
    padding: 15px;
  }
}
</style>
