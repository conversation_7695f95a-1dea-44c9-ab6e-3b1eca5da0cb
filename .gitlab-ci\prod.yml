# 定义安装依赖的作业
install_dependencies_for_prod:
  stage: install
  tags:
    - pnpm
  script:
    - pnpm install
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'
  cache:
    key: ${CI_PROJECT_NAME}-prod
    paths:
      - node_modules/
      - packages/utils/node_modules/

build_main_project_for_prod:
  stage: build
  tags:
    - pnpm
  dependencies:
    - install_dependencies_for_prod
  needs:
    - install_dependencies_for_prod
  script:
    - pnpm run build:prod # 执行构建命令
  artifacts:
    paths:
      - dist/ # 保存构建后的输出
    expire_in: 1 week # 构建产物保存一周
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'
  cache:
    key: ${CI_PROJECT_NAME}-prod
    paths:
      - node_modules/
      - packages/utils/node_modules/

build_main_image_for_prod:
  stage: build_img
  tags:
    - host-docker-builder
  dependencies:
    - build_main_project_for_prod
  needs:
    - build_main_project_for_prod
  before_script:
    - echo "登录docker镜像仓库"
    - echo $PROD_DOCKER_REGISTRY
    - echo "$ACCESS_KEY_SECRET" | docker login $PROD_DOCKER_REGISTRY --username $ACCESS_KEY_ID --password-stdin
  variables:
    TZ: Asia/Shanghai
  script:
    - echo "镜像tag为:"
    - echo $CI_COMMIT_TAG
    - echo ">>>>>>>>>>>>>>>>>>>>开始构建应用${IMAGE_NAME}镜像<<<<<<<<<<<<<<<<<<<<<<<<<<<<"
    - docker build -t $PROD_DOCKER_REGISTRY/recircle/$IMAGE_NAME:$CI_COMMIT_TAG .
    - echo ">>>>>>>>>>>>>>>>>>>>推送应用${IMAGE_NAME}镜像到Harbor仓库<<<<<<<<<<<<<<<<<<<<<<<<<<<<"
    - docker build -t $PROD_DOCKER_REGISTRY/recircle/$IMAGE_NAME:$CI_COMMIT_TAG .
    - docker push $PROD_DOCKER_REGISTRY/recircle/$IMAGE_NAME:$CI_COMMIT_TAG
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'

deploy_main_k8s_for_prod:
  stage: deploy
  tags:
    - k8s-prod
  dependencies:
    - build_main_image_for_prod
  needs:
    - build_main_image_for_prod
  variables:
    TZ: Asia/Shanghai
  script:
    - kubectl patch deployment $IMAGE_NAME-v1 -n recircle-industry-platform-prod -p "{\"spec\":{\"template\":{\"spec\":{\"containers\":[{\"name\":\"$IMAGE_NAME\",\"image\":\"$PROD_DOCKER_REGISTRY/recircle/$IMAGE_NAME:${CI_COMMIT_TAG}\"}]}}}}"
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'
