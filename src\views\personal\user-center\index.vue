<script setup lang="ts">
import { getUserTenantList, type TenantInfo } from '@/api/personal'
import { briefIcon, emailIcon, locationIcon, organizationIcon, positionIcon } from '@/assets/base64'
import { tabNameEnum } from '@/enum'
import { useUserStore } from '@/store/modules/user'
import TenantCard from './components/TenantCard.vue'

/**
 * 用户信息
 */
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)

/**
 * 用户信息列表
 */
const userInfoList = ref([
  {
    icon: positionIcon,
    key: 'postName',
  },
  {
    icon: emailIcon,
    key: 'email',
  },
  {
    icon: locationIcon,
    key: ['provinceName', 'cityName'],
  },
  {
    icon: briefIcon,
    key: 'intro',
  },
  {
    icon: organizationIcon,
    key: 'orgDeptPost',
  },
])

/**
 * 获取用户信息对应字段
 */
const getUserInfo = (key: string | string[], joinSymbol = '-') => {
  if (Array.isArray(key)) {
    const arr = key.map(item => userInfo.value?.[item as keyof typeof userInfo.value])
    // 当前函数适用于2个字段拼接, 如果是3个字段拼接, 需要修改
    if (!arr[0])
      return ''
    return arr[arr.length - 1] ? arr.join(joinSymbol) : arr[0]
  }
  return userInfo.value?.[key as keyof typeof userInfo.value]
}

/**
 * 激活的标签
 */
const activeName = ref<tabNameEnum>(tabNameEnum.TENANT)
/**
 * 租户列表
 */
const tenants = ref<TenantInfo[]>([])

/**
 * 初始化,获取租户列表
 */
const hasInit = ref<boolean>(false)
const init = async () => {
  try {
    const { usersTenantList } = await getUserTenantList(userInfo.value?.id as string)
    tenants.value = usersTenantList || []
  } finally {
    hasInit.value = true
  }
}

onMounted(() => {
  init()
})
</script>

<template>
  <div class="personal-page rounded-md bg-white text-sm text-sk-text">
    <div class="mb-5 mt-2.5 flex">
      <div class="h-20 w-20 flex-shrink-0 overflow-hidden rounded-1/2">
        <img :src="userInfo?.avatar" alt="用户头像" class="h-full w-full object-cover">
      </div>
      <div class="ml-5 w-full">
        <div class="w-full text-base font-bold">
          欢迎您-{{ userInfo?.username }}
        </div>
        <div class="w-full flex flex-wrap">
          <div v-for="item in userInfoList" :key="item.icon" w="1/2" mt-5 flex items-start>
            <span mt-0.5 flex-center shrink-0>
              <img :src="item.icon" class="h-4 w-4">
            </span>
            <span ml-2.5 pr-2.5>{{ getUserInfo(item.key) }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="mt-8">
      <el-tabs v-model="activeName" class="mb-4">
        <el-tab-pane :label="`租户(${tenants.length})`" :name="tabNameEnum.TENANT">
          <div v-if="hasInit && tenants.length" class="flex flex-wrap">
            <TenantCard
              v-for="tenant in tenants"
              :key="tenant.id"
              :data="tenant"
              class="tenant-card mr-27px"
            />
          </div>
          <div v-if="hasInit && !tenants.length" class="h-200px flex flex-center">
            <p>
              暂无租户
            </p>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<style scoped>
.tenant-card:nth-child(3n) {
  margin-right: 0;
}
</style>
