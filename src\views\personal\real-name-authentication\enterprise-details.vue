<script setup lang="ts">
import { getMyEnterpriseDetails, getRealNameAuthentication } from '@/api/personal/real-name-authentication'
import { ENTERPRISE_TYPE, INDUSTRY, LISTED_TYPE, REAL_NAME_STATUS, STATUS_OPTIONS, YESORNO_OPTIONS } from '@/constant'
import dayjs from 'dayjs'

defineOptions({
  name: 'EnterpriseDetails',
})
const { userInfo } = useUserStore()
const dataDetail = ref<any>({})
const personalDetail = ref<any>({})
const activeName = ref('basics')
async function getAccountInfoApi() {
  dataDetail.value = await getMyEnterpriseDetails()
  personalDetail.value = await getRealNameAuthentication(userInfo!.id as string)
}
onBeforeMount(() => {
  getAccountInfoApi()
})
</script>

<template>
  <div class="basic-setting personal-page">
    <el-descriptions title="企业认证" :column="2">
      <el-descriptions-item label="实名状态">
        {{ REAL_NAME_STATUS.find(item => item.value === personalDetail.realNameStatus)?.label }}
      </el-descriptions-item>
      <el-descriptions-item label="用户账号ID">
        {{ personalDetail.id }}
      </el-descriptions-item>
      <el-descriptions-item label="注册时间">
        {{ dayjs(personalDetail.createTime).format('YYYY-MM-DD HH:mm:ss') }}
      </el-descriptions-item>
      <el-descriptions-item label="用户账号状态">
        {{ STATUS_OPTIONS.find(item => item.value === personalDetail.status)?.label }}
      </el-descriptions-item>
      <el-descriptions-item label="最后登录时间">
        {{ dayjs(personalDetail.loginDate).format('YYYY-MM-DD HH:mm:ss') }}
      </el-descriptions-item>
    </el-descriptions>
    <div class="h-15px w-[calc(100%+60px)] bg-[#f5f7f9] -ml-7.5" />
    <template v-if="dataDetail.auditStatus !== 1">
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane label="基础信息" name="basics" />
        <el-tab-pane label="工商信息" name="business" />
      </el-tabs>
      <el-descriptions title="" :column="2">
        <template v-if="activeName === 'basics'">
          <el-descriptions-item label="公司名称" width="470px">
            {{ dataDetail.baseInfo?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="公司简介" width="470px">
            {{ dataDetail.baseInfo?.alias }}
          </el-descriptions-item>
          <el-descriptions-item label="统一社会信用代码" width="470px">
            {{ dataDetail.baseInfo?.creditCode }}
          </el-descriptions-item>
          <el-descriptions-item label="公司法定代表人" width="470px">
            {{ dataDetail.baseInfo?.legalPersonName }}
          </el-descriptions-item>
          <el-descriptions-item label="是否上市" width="470px">
            {{ YESORNO_OPTIONS.find(item => item.value === dataDetail.baseInfo?.isListed)?.label }}
          </el-descriptions-item>
          <el-descriptions-item label="公司规模" width="470px">
            {{ dataDetail.baseInfo?.staffNumRange }}
          </el-descriptions-item>
          <el-descriptions-item label="所属行业" width="470px">
            {{ INDUSTRY.find(item => item.value === dataDetail.baseInfo?.industry)?.label }}
          </el-descriptions-item>
          <el-descriptions-item label="所在区域" width="470px">
            {{ dataDetail.baseInfo?.region }}
          </el-descriptions-item>
          <el-descriptions-item label="邮政编码" width="470px">
            {{ dataDetail.baseInfo?.postalCode }}
          </el-descriptions-item>
          <el-descriptions-item label="通讯地址" width="470px">
            {{ dataDetail.baseInfo?.contactAddress }}
          </el-descriptions-item>
          <el-descriptions-item label="注册地址" width="470px">
            {{ dataDetail.baseInfo?.registAddress }}
          </el-descriptions-item>
          <el-descriptions-item label="创立日期" width="470px">
            {{ dayjs(dataDetail.baseInfo?.registTime).format('YYYY-MM-DD HH:mm:ss') }}
          </el-descriptions-item>
          <el-descriptions-item label="公司网址" width="470px">
            {{ dataDetail.baseInfo?.website }}
          </el-descriptions-item>
          <el-descriptions-item label="联系人姓名" width="470px">
            {{ dataDetail.baseInfo?.contactName }}
          </el-descriptions-item>
          <el-descriptions-item label="联系人电话" width="470px">
            {{ dataDetail.baseInfo?.contactPhoneNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="联系人邮箱" width="470px">
            {{ dataDetail.baseInfo?.contactEmail }}
          </el-descriptions-item>
        </template>
        <template v-else>
          <el-descriptions-item label="企业名称" width="470px">
            {{ dataDetail.businessInfo?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="企业性质" width="470px">
            {{ ENTERPRISE_TYPE.find(item => item.value === dataDetail.businessInfo?.enterpriseType)?.label }}
          </el-descriptions-item>
          <el-descriptions-item label="企业类型" width="470px">
            {{ dataDetail.businessInfo?.companyOrgType }}
          </el-descriptions-item>
          <el-descriptions-item label="登记机关" width="470px">
            {{ dataDetail.businessInfo?.regInstitute }}
          </el-descriptions-item>
          <el-descriptions-item label="是否上市" width="470px">
            {{ YESORNO_OPTIONS.find(item => item.value === dataDetail.businessInfo?.isListed)?.label }}
          </el-descriptions-item>
          <el-descriptions-item label="股票代码" width="470px">
            {{ dataDetail.businessInfo?.bondNum }}
          </el-descriptions-item>
          <el-descriptions-item label="上市类型" width="470px">
            {{ LISTED_TYPE.find(item => item.value === dataDetail.businessInfo?.listedType)?.label }}
          </el-descriptions-item>
          <el-descriptions-item label="曾用名" width="470px">
            {{ dataDetail.businessInfo?.historyNames }}
          </el-descriptions-item>
          <el-descriptions-item label="登记状态" width="470px">
            {{ dataDetail.businessInfo?.regStatus }}
          </el-descriptions-item>
          <el-descriptions-item label="注册资本" width="470px">
            {{ dataDetail.businessInfo?.regCapital }}
          </el-descriptions-item>
          <el-descriptions-item label="所属工商局" width="470px">
            {{ dataDetail.businessInfo?.bureau }}
          </el-descriptions-item>
          <el-descriptions-item label="营业期限始" width="470px">
            {{ dayjs(dataDetail.businessInfo?.fromTime).format('YYYY-MM-DD') }}
          </el-descriptions-item>
          <el-descriptions-item label="营业结束至" width="470px">
            {{ dayjs(dataDetail.businessInfo?.toTime).format('YYYY-MM-DD') }}
          </el-descriptions-item>
          <el-descriptions-item label="核准日期" width="470px">
            {{ dayjs(dataDetail.businessInfo?.approvedTime).format('YYYY-MM-DD') }}
          </el-descriptions-item>
          <el-descriptions-item label="法定代表人名称" width="470px">
            {{ dataDetail.businessInfo?.legalPersonName }}
          </el-descriptions-item>
          <el-descriptions-item label="成立日期" width="470px">
            {{ dayjs(dataDetail.businessInfo?.estiblishTime).format('YYYY-MM-DD') }}
          </el-descriptions-item>
          <el-descriptions-item label="组织机构代码" width="470px">
            {{ dataDetail.businessInfo?.orgNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="企业注册号" width="470px">
            {{ dataDetail.businessInfo?.regNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="吊销日期" width="470px">
            {{ dayjs(dataDetail.businessInfo?.revokeLocalDateTime).format('YYYY-MM-DD') }}
          </el-descriptions-item>
          <el-descriptions-item label="统一社会信用代码" width="470px">
            {{ dataDetail.businessInfo?.creditCode }}
          </el-descriptions-item>
          <el-descriptions-item label="注册地址" width="470px">
            {{ dataDetail.businessInfo?.regLocation }}
          </el-descriptions-item>
          <el-descriptions-item label="生产经营许可证" width="470px">
            {{ dataDetail.businessInfo?.sc }}
          </el-descriptions-item>
          <el-descriptions-item label="经营范围">
            {{ dataDetail.businessInfo?.businessScope }}
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </template>
    <el-empty v-else description="您的实名认证信息已提交，请耐心等候" />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-descriptions__label) {
  color: #a5a5a5;
}
:deep(.el-descriptions__content) {
  word-break: break-all;
}
.PERSON {
  background-image: url('@/assets/imgs/person-certification.png');
}

.ENTERPRISE {
  background-image: url('@/assets/imgs/enterprise-certification.png');
}

.text-custom-color {
  color: #777777;
}

.background-color {
  background-color: #5180f3;
  border-radius: 5px;
  color: #fff;
  line-height: 36px;
  text-align: center;
}
</style>
