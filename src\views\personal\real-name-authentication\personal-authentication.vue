<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { addPersonalAuthentication } from '@/api/personal/real-name-authentication'
import { GENDER_OPTIONS } from '@/constant'
import { REG } from '@/constant/reg'
import { Back, CircleCheckFilled } from '@element-plus/icons-vue'

defineOptions({
  name: 'PersonalAuthentication',
})
const { userInfo } = useUserStore()
const router = useRouter()
const formRef = useTemplateRef<FormInstance>('formRef')
const baseInfo = ref({}) as Ref
const isSubmit = ref(true)
const { PHONE, ID_CARD } = REG
const rules = reactive<FormRules<typeof baseInfo>>({
  mobile: [
    { required: true, message: '请输入', trigger: 'blur' },
    { pattern: PHONE, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  name: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  sex: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  nameSpelling: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  idType: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  idNumber: [
    { required: true, message: '请输入', trigger: 'blur' },
    { pattern: ID_CARD, message: '请输入正确的身份证号', trigger: 'blur' },
  ],
  age: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
})
async function submitForm() {
  formRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        const res = await addPersonalAuthentication({
          ...baseInfo.value,
          userId: userInfo?.id,
        }) as any
        console.log(res, '此处返回是res.data')
        $message.success('提交成功')
        isSubmit.value = false
        console.log(res)
      } catch (error) {
        console.log(error)
      }
    }
  })
}
onBeforeMount(() => {

})
</script>

<template>
  <div class="basic-setting personal-page">
    <div class="flex">
      <el-icon :size="22" class="cursor-pointer">
        <Back @click="router.go(-1)" />
      </el-icon>
      <span class="personal-page-title ml-2.5">个人认证</span>
    </div>
    <el-steps style="max-width: 600px; margin-left: 170px;margin-bottom: 60px;" :active="isSubmit ? 1 : 2" align-center>
      <el-step title="填写实名信息" :icon="CircleCheckFilled" />
      <el-step title="提交完成" :icon="CircleCheckFilled" />
    </el-steps>
    <template v-if="isSubmit">
      <el-form ref="formRef" :model="baseInfo" :rules="rules" label-width="auto" label-position="right">
        <el-form-item label="实名姓名:" prop="name">
          <el-input v-model="baseInfo.name" placeholder="请输入" :maxlength="30" clearable />
        </el-form-item>
        <el-form-item label="性别:" prop="sex">
          <el-select v-model="baseInfo.sex" placeholder="请选择" clearable>
            <el-option v-for="item in GENDER_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="拼音:" prop="nameSpelling">
          <el-input v-model="baseInfo.nameSpelling" placeholder="请输入" :maxlength="30" clearable />
        </el-form-item>
        <el-form-item label="证件类型:" prop="idType">
          <el-select v-model="baseInfo.idType" placeholder="请选择">
            <el-option label="身份证" value="sfz" />
          </el-select>
        </el-form-item>
        <el-form-item label="证件号码:" prop="idNumber">
          <el-input v-model="baseInfo.idNumber" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="年龄:" prop="age">
          <el-input v-model="baseInfo.age" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="手机号码:" prop="mobile">
          <el-input v-model="baseInfo.mobile" placeholder="请输入" clearable />
        </el-form-item>
      </el-form>
      <div class="mt-6 h-9 flex justify-center">
        <el-button type="primary" @click="submitForm">
          下一步
        </el-button>
      </div>
    </template>
    <template v-else>
      <el-result
        class="ml-56.5 w-121.75" icon="success" sub-title="*个人实名认证请确保您所填写的信息与您的相关证件信息一致准确，
我们将在24小时内进行审核，并将相关结果进行短信通知，请确保您
的手机号码准确畅通"
      >
        <template #extra>
          <el-button
            type="primary" @click="() => {
              router.push({ name: 'PersonalDetails' })
            }"
          >
            确定
          </el-button>
        </template>
      </el-result>
    </template>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-descriptions__label) {
  color: #a5a5a5;
}

.but {
  width: 83px;
  height: 36px;
  border-radius: 5px;
  text-align: center;
  line-height: 36px;
  border: 1px solid #e4e4e4;
  font-size: 14px;
}

.bg-color {
  background: #5180f3;
  color: #fff;
}

.basic-setting {
  :deep(.el-form) {
    display: flex;
    flex-wrap: wrap;

    .el-form-item {
      width: 470px;
      margin: 0 0px 20px 168px;

      .el-form-item__label {
        margin-bottom: 5px;
      }

      .el-form-item__content {
        > .el-input {
          height: 34px;
        }
      }
    }

    .w-full {
      width: 100%;
    }
  }
}
</style>
