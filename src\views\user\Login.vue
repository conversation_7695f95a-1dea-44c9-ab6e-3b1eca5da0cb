<script setup lang="ts">
import ARTISTIC from '@/assets/imgs/artistic-login.png'
import LOGO from '@/assets/imgs/logo.png'
import { LoginTabEnum } from '@/enum'
import { handleToWebsite } from '@/utils'
import ForgotPassword from './components/ForgotPassword/index.vue'
import SignIn from './components/SignIn/index.vue'
import SignUp from './components/SignUp/index.vue'

const componentMap = {
  [LoginTabEnum.SIGN_IN]: SignIn,
  [LoginTabEnum.SIGN_UP]: SignUp,
  [LoginTabEnum.FORGOT_PASSWORD]: ForgotPassword,
} as const
const currTab = ref<LoginTab>('signIn')

provide('currTab', currTab)
</script>

<template>
  <div pos-relative h-full w-full select-none class="bg-login">
    <img :src="LOGO" alt="" absolute inset-0 left-14 top-10 cursor-pointer @click="handleToWebsite">
    <div wh-full flex>
      <div h-full flex flex-col items-center justify-center class="w-4/7">
        <div>
          <p font-bold>
            <span text-42px>瑟壳-</span>
            <span text-42px text-sk-primary>铜产业互联平台</span>
          </p>
          <p text-28px>
            先进制造业 先用瑟壳
          </p>
        </div>
        <img :src="ARTISTIC" alt="" mt-14 class="w-125">
      </div>
      <div flex-center>
        <component :is="componentMap[currTab]" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.bg-login {
  background-image: url('@/assets/imgs/bg-login.jpg');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
</style>
