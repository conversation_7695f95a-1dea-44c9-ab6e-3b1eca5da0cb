<script setup lang="ts">
import { ElMessage } from 'element-plus'

function handleSetting() {
  ElMessage.warning('该功能后续开发')
}
</script>

<template>
  <div>
    <div class="content">
      <span class="title">密保问题</span>
      <span class="info">未设置密保问题，密保问题可有效保护账号安全</span>
    </div>
    <el-button
      type="primary"
      link
      @click="handleSetting"
    >
      设置
    </el-button>
  </div>
</template>

<style lang="scss" scoped></style>
