import type * as Login from './types'
import request from '@/service/axios'

/** 用户注册 */
export function register(data: Login.RegisterReqData) {
  return request.post<string>({
    url: '/user-center/user/register',
    data,
  })
}

/** 发送验证码-注册 */
export function sendCode(data: Pick<Login.RegisterReqData, 'mobile'>) {
  return request.post<boolean>({
    url: '/user-center/user/getVerifyCode',
    data,
  })
}

/** 忘记密码 */
export function forgetPassword(data: Login.ForgetPasswordReqData) {
  return request.post<boolean>({
    url: '/user-center/user/forgetPassword',
    data,
  })
}

/** 发送验证码-注册 */
export function sendCodeByLogin(data: Pick<Login.RegisterReqData, 'mobile'>) {
  return request.post<boolean>({
    url: '/user-center/auth/getVerifyCode',
    data,
  })
}

/** 发送验证码-忘记密码 */
export function sendCodeForForgetPassword(data: Pick<Login.RegisterReqData, 'mobile'>) {
  return request.post<boolean>({
    url: '/user-center/user/getVerifyCodeV1',
    data,
  })
}

/** 手机验证码登录 */
export function loginByMobile(data: Login.LoginMobileReqData) {
  return request.post<Login.LoginResData>({
    url: '/user-center/auth/portalSmsLogin',
    data,
  })
}

/** 账号密码登录 */
export function loginByPassword(data: Login.LoginPasswordReqData) {
  return request.post<Login.LoginResData>({
    url: '/user-center/auth/portalUserNameLogin',
    data,
  })
}

/** 退出登录 */
export function postLogout() {
  return request.post<boolean>({
    url: '/user-center/auth/portalLogout',
  })
}

/** 获取用户详情 */
export function getUserInfo() {
  return request.get<Login.UserInfoResData>({
    url: 'users/info',
  })
}

/** 修改用户账号 */
export function updateUserName(data: { userName: string }) {
  return request.put<boolean>({
    url: '/user-center/user/update-username',
    data,
  })
}
