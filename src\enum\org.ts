/** 组织类型 */
export enum OrganizeTypeEnum {
  /** 未实名 */
  GROUP = 'jt',
  /** 已实名 */
  COMPANY = 'gs',
}

/** 组织类型显示 */
export const OrganizeTypeEnumText = {
  [OrganizeTypeEnum.GROUP]: '集团',
  [OrganizeTypeEnum.COMPANY]: '公司',
}

/** 证件类型 */
export enum CertificateTypeEnum {
  /** 身份证 */
  ID_CARD = 'sfz',
}

/** 证件类型显示 */
export const CertificateTypeEnumText = {
  [CertificateTypeEnum.ID_CARD]: '身份证',
}
