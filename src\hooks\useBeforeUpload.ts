import type { UploadFile, UploadRawFile } from 'element-plus'
import { ElMessage } from 'element-plus'

interface Options {
  /** 是否限制文件类型 */
  isAccept?: boolean
  /** 接受上传的文件类型 */
  accept?: string
  /** 上传文件类型不符合提示语 */
  acceptMsg?: string
  /** 是否限制文件大小 */
  isLimitSize?: boolean
  /** 限制上传文件大小 */
  limitSize?: number
  /** 上传文件大小不符合提示语 */
  limitMsg?: string
  /** 文件名长度限制（去除扩展名） - 默认100个字符 */
  fileNameLength?: number
}

/**
 * 上传文件前的处理函数
 * @param file 要上传的文件
 * @param options 上传文件的选项
 * @returns 是否允许上传
 */
export function useBeforeUpload(file: UploadFile | UploadRawFile, options?: Options) {
  const { isAccept, accept, acceptMsg, isLimitSize, limitSize, limitMsg, fileNameLength = 100 } = options || {}

  // 去除后缀名的文件名
  const fileName = file.name.replace(/\.[^/.]+$/, '')
  if (fileName.length > fileNameLength) {
    ElMessage.warning(`当前文件"${file.name}"的文件名长度超过${fileNameLength}个字符，请修改文件名`)
    return false
  }

  // 文件格式
  const fileReg = file.name.replace(/.+\./, '')

  if (isAccept && accept && !accept.includes(fileReg.toLowerCase())) {
    ElMessage.warning(`当前文件"${file.name}"的文件类型为${fileReg}，${acceptMsg}`)
    return false
  }

  // 大小
  const size = (file.size ?? 0) / 1024 / 1024
  if (isLimitSize && limitSize && size > limitSize) {
    ElMessage.warning(`当前文件"${file.name}"的文件大小为${size.toFixed(2)}MB，${limitMsg}`)
    return false
  }

  return true
}
