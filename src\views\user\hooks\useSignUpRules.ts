import type * as Login from '@/api/user/types'
import type { FormRules } from 'element-plus'
import { HAS_TWO_TYPES_UNDERLINE_REG, PHONE_REG } from '@2030/utils'

export function useRules(formData: Login.RegisterReqData) {
  // 手机号验证状态
  const isMobileValid = ref(false)

  /**
   * 表单验证规则
   */
  const formRules: FormRules<Login.RegisterReqData> = reactive({
    mobile: [
      { required: true, message: '请输入', trigger: 'blur' },
      { validator: (_rule, value, callback) => validateMobile(value, callback), trigger: 'blur' },
    ],
    captchaCode: [
      { required: true, message: '请输入验证码', trigger: 'blur' },
      { pattern: /^\d{6}$/, message: '验证码不符', trigger: 'blur' },
    ],
    password: [
      { required: true, message: '请输入 ', trigger: 'blur' },
      { min: 4, max: 16, message: '密码4-16位', trigger: 'blur' },
      { pattern: HAS_TWO_TYPES_UNDERLINE_REG, message: '只能字母/数字/下划线,至少两种', trigger: 'blur' },
    ],
    rePassword: [
      { required: true, message: '请输入', trigger: 'blur' },
      { validator: (_rule, value, callback) => validateRePassword(value, callback, formData), trigger: 'blur' },
    ],
  })

  /**
   * 验证两次密码是否一致
   */
  function validateRePassword(value: string, callback: (error?: Error) => void, formData: Login.RegisterReqData) {
    if (value === formData.password) {
      callback()
    } else {
      callback(new Error('密码不一致'))
    }
  }

  /**
   * 验证手机号
   */
  function validateMobile(value: string, callback: (error?: Error) => void) {
    isMobileValid.value = PHONE_REG.test(value)
    if (isMobileValid.value) {
      callback()
    } else {
      callback(new Error('手机号不符'))
    }
  }

  return { formRules, isMobileValid }
}
