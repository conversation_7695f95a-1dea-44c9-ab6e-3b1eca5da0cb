import { $t } from '@/plugins/i18n'

export default [
  {
    path: '/help',
    name: 'Help',
    component: () => import('@/views/share/Help.vue'),
    meta: {
      title: $t('router.help'),
    },
  },
  {
    path: '/privacy',
    name: 'Privacy',
    component: () => import('@/views/share/Privacy.vue'),
    meta: {
      title: $t('router.privacy'),
    },
  },
  {
    path: '/terms',
    name: 'Terms',
    component: () => import('@/views/share/Terms.vue'),
    meta: {
      title: $t('router.terms'),
    },
  },
]
