<!--
  密码强度校验
  @params password 密码
  @params offsetLeft 左边偏移距离
  @params offsetTop 顶部偏移距离

  @example - (/src/views/user/components/SignUp/index.vue)
  <PasswordStrength :password="formParams.password" :offset-top="passwordError ? '0px' : '-15px'" offset-left="70px" />
-->
<script setup lang="ts">
import { checkPasswordStrength, type PasswordStrength } from './data'

const { password, offsetLeft = '0px', offsetTop = '0px' } = defineProps<{
  // 密码
  password: string
  // 左边offset距离
  offsetLeft?: string
  // 顶部offset距离
  offsetTop?: string
}>()

const strengthText = computed(() => {
  const map: Record<PasswordStrength, string> = {
    'weak': '弱',
    'medium': '中',
    'strong': '强',
    '': '',
  }
  return map[checkPasswordStrength(password)]
})

const strengthColor = computed(() => {
  const map: Record<PasswordStrength, string> = {
    'weak': 'var(--el-color-danger)',
    'medium': 'var(--el-color-warning)',
    'strong': 'var(--el-color-success)',
    '': '',
  }
  return map[checkPasswordStrength(password)]
})

const getStrengthClass = computed(() => (level: number) => {
  const strength = checkPasswordStrength(password)
  if (!strength)
    return ''
  const strengthMap = {
    weak: { level: 1, color: 'bg-[var(--el-color-danger)]' },
    medium: { level: 2, color: 'bg-[var(--el-color-warning)]' },
    strong: { level: 3, color: 'bg-[var(--el-color-success)]' },
  }
  const currentStrength = strengthMap[strength]
  return level > currentStrength.level
    ? 'bg-[var(--sk-E4)]'
    : currentStrength.color
})
</script>

<template>
  <div v-if="password" class="password-strength" :style="{ marginTop: offsetTop, marginLeft: offsetLeft }" mb-2 flex text-xs text-sk-text-light>
    <div class="strength-indicator" flex items-center>
      <div
        v-for="level in 3"
        :key="level"
        mr-0.5 h-5px w-15
        :class="[getStrengthClass(level)]"
      />
      <span ml-2 :style="{ color: strengthColor }">{{ strengthText }}</span>
    </div>
  </div>
</template>
