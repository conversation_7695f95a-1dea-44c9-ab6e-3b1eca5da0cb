# 定义安装依赖的作业
install_dependencies_for_release:
  stage: install
  tags:
    - pnpm
  script:
    - pnpm install --no-frozen-lockfile
  only:
    - release
  cache:
    key: ${CI_PROJECT_NAME}-release
    paths:
      - node_modules/
      - packages/utils/node_modules/

build_main_project_for_release:
  stage: build
  tags:
    - pnpm
  dependencies:
    - install_dependencies_for_release
  needs:
    - install_dependencies_for_release
  script:
    - pnpm run build:stage # 执行构建命令
  artifacts:
    paths:
      - dist/ # 保存构建后的输出
    expire_in: 1 week # 构建产物保存一周
  only:
    - release
  cache:
    key: ${CI_PROJECT_NAME}-release
    paths:
      - node_modules/
      - packages/utils/node_modules/

build_main_image_for_release:
  stage: build_img
  tags:
    - host-docker-builder
  dependencies:
    - build_main_project_for_release
  needs:
    - build_main_project_for_release
  before_script:
    - echo "$DOCKER_REGISTRY_PASS" | docker login $DOCKER_REGISTRY --username $DOCKER_REGISTRY_USER --password-stdin
  variables:
    TZ: Asia/Shanghai
  script:
    - export CURRENT_TIME=$(TZ="Asia/Shanghai" date '+%Y-%m-%dT%H')
    - echo "docker image tag is:"
    - echo $CURRENT_TIME
    - docker build -t $DOCKER_REGISTRY/recircle-industry-platform/$IMAGE_NAME:$CURRENT_TIME .
    - docker push $DOCKER_REGISTRY/recircle-industry-platform/$IMAGE_NAME:$CURRENT_TIME
  only:
    - release

deploy_main_k8s_for_release:
  stage: deploy
  tags:
    - kube-deployer
  dependencies:
    - build_main_image_for_release
  needs:
    - build_main_image_for_release
  variables:
    TZ: Asia/Shanghai
  script:
    - export CURRENT_TIME=$(TZ="Asia/Shanghai" date '+%Y-%m-%dT%H')
    - echo "deploy docker image tag is:"
    - echo $CURRENT_TIME
    - kubectl patch deployment $IMAGE_NAME-v1 --context=<EMAIL> -n recircle-industry-platform-preview -p "{\"spec\":{\"template\":{\"spec\":{\"containers\":[{\"name\":\"$IMAGE_NAME\",\"image\":\"harbor.irecircle.com/recircle-industry-platform/$IMAGE_NAME:${CURRENT_TIME}\"}]}}}}"
  only:
    - release
