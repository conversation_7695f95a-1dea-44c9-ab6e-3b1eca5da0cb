import type * as Login from '@/api/user/types'
import type { FormRules } from 'element-plus'
import { PHONE_REG } from '@2030/utils'

export const FORM_RULES: FormRules<Login.ForgetPasswordReqData> = {
  username: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  mobile: [
    { required: true, message: '请输入', trigger: 'blur' },
    { pattern: PHONE_REG, message: '请输入正确的手机号', trigger: 'blur' },
  ],
  captchaCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码不符', trigger: 'blur' },
  ],
}
