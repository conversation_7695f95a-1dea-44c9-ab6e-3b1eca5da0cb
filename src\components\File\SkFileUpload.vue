<script setup lang="ts">
import { type FileInfo, type FileUploaderProps, useUpload } from '@/components/File/useUpload'
import { ElButton, ElUpload } from 'element-plus'
import SkAnnex from './SkAnnex.vue'

const props = withDefaults(defineProps<FileUploaderProps>(), {
  tip: '最多上传10个文件，单个文件10MB以内，格式支持jpg/png/pdf/docx/xlsx/ppt/txt/zip/rar等',
  isAutoTip: false,
  isShowTip: true,
  multiple: true,
  accept: '.jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar,.gz,.bz2,.7z,.tar,.tgz,.tar.gz,.tar.bz2,.gif',
  isAccept: true,
  acceptMsg: '请上传符合格式的文件类型',
  limit: 10,
  isLimitSize: true,
  limitSize: 10,
  limitMsg: '请上传符合大小的文件',
  uploadUrl: import.meta.env.VITE_OSS_API,
})

const modelFiles = defineModel<FileInfo[]>({ default: [] })

const { uploadRef, fileList, getTip, handleChange, handleRemoveFile, handleExceed } = useUpload('file', props, modelFiles)
</script>

<template>
  <div class="sk-file-upload-wrapper sk-upload">
    <ElUpload
      ref="uploadRef"
      class="sk-file-upload"
      :file-list="fileList"
      action="#"
      :auto-upload="false"
      v-bind="uploadAttrs"
      :multiple="multiple"
      :accept="accept"
      :limit="limit"
      @change="handleChange"
      @exceed="handleExceed"
    >
      <ElButton class="sk-file-upload-btn">
        附件上传
      </ElButton>
      <template #tip>
        <div
          v-if="isShowTip"
          class="el-upload__tip"
        >
          {{ getTip }}
        </div>
      </template>
      <template #file="{ file }">
        <SkAnnex
          :file-info="file"
          :is-show-download="false"
          :is-show-delete="!uploadAttrs?.disabled"
          file-url="url"
          file-name="name"
          @remove="handleRemoveFile"
        />
      </template>
    </ElUpload>
  </div>
</template>

<style lang="scss" scoped>
.sk-file-upload-wrapper {
  .sk-file-upload-btn {
    width: 76px;
    height: 30px;
    color: #5180f3;
    border: 1px solid #5180f3;
    border-radius: '5px';
  }

  .el-upload-list {
    width: 450px;

    .el-upload-list__item {
      display: flex;
      align-items: center;

      width: 100%;
      height: 20px;
    }

    .el-upload-list__item:hover {
      background-color: transparent;
    }
  }
}
</style>
