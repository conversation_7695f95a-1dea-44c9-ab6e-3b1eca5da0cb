/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElResult: typeof import('element-plus/es')['ElResult']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElStep: typeof import('element-plus/es')['ElStep']
    ElSteps: typeof import('element-plus/es')['ElSteps']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    'IEp:apple': typeof import('~icons/ep/apple')['default']
    'IEp:back': typeof import('~icons/ep/back')['default']
    'IEp:caretBottom': typeof import('~icons/ep/caret-bottom')['default']
    'IEp:circlePlus': typeof import('~icons/ep/circle-plus')['default']
    IEpDelete: typeof import('~icons/ep/delete')['default']
    'ISvg:code': typeof import('~icons/svg/code')['default']
    'ISvg:home': typeof import('~icons/svg/home')['default']
    'ISvg:iconParkSolidApple': typeof import('~icons/svg/icon-park-solid-apple')['default']
    'ISvg:phone': typeof import('~icons/svg/phone')['default']
    'ISvg:success': typeof import('~icons/svg/success')['default']
    PasswordStrength: typeof import('./src/components/PasswordStrength/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SkAnnex: typeof import('./src/components/File/SkAnnex.vue')['default']
    SkFileUpload: typeof import('./src/components/File/SkFileUpload.vue')['default']
    SkTipDialog: typeof import('./src/components/TipDialog/SkTipDialog.vue')['default']
  }
}
