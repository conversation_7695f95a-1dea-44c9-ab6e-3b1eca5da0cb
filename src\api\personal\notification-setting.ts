import request from '@/service/axios'

/**
 * @file 个人设置-通知设置相关API
 */

/** 通知设置信息 */
export interface GetNotificationSettingRes {
  createTime: number
  updateTime: number
  creator: string
  updater: string
  deleted: null
  updaterName: string
  creatorName: string
  id: string
  userId: string
  /** 消息通知 0开启 1关闭 */
  msgNotify: EnableNum
  /** 系统通知 0开启 1关闭 */
  systemNotify: EnableNum
  /** 待办通知 0开启 1关闭 */
  todoNotify: EnableNum
}

/** 修改通知设置信息参数 */
export interface UpdateNotificationSettingReq {
  msgNotify?: EnableNum
  systemNotify?: EnableNum
  todoNotify?: EnableNum
}

/** 获取通知设置信息 */
export const getNotificationSetting = () => request.get<GetNotificationSettingRes>({ url: '/tenant/base/msg/notify/config' })

/** 修改通知设置信息 */
export const updateNotificationSetting = (data: UpdateNotificationSettingReq) => request.post<boolean>({ url: '/tenant/base/msg/notify/update-notify', data })
