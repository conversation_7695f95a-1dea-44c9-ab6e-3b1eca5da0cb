<script setup lang="ts">
import SignUpForm from './SignUpForm.vue'
import SignUpResult from './SignUpResult.vue'

const componentMap = {
  signUpForm: SignUpForm,
  signUpResult: SignUpResult,
} as const
const currComponent = ref<'signUpForm' | 'signUpResult'>('signUpForm')
provide('currComponent', currComponent)

const userName = ref<string>('')
provide('userName', userName)
</script>

<template>
  <div>
    <component :is="componentMap[currComponent]" />
  </div>
</template>
