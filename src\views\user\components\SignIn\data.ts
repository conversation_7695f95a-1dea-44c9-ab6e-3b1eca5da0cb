import type * as Login from '@/api/user/types'
import type { FormRules } from 'element-plus'
import { PHONE_REG } from '@2030/utils'

/**
 * 登录方式
 */
export const TABS = [
  {
    label: '账密登录',
    name: 'password',
  },
  {
    label: '验证码登录',
    name: 'mobile',
  },
]

/**
 * 底部链接
 */
export const FOOTER_LINKS = [
  { name: '帮助', routerName: 'Help' },
  { name: '隐私', routerName: 'Privacy' },
  { name: '条款', routerName: 'Terms' },
]

/**
 * 账号密码登录表单规则
 */
export const PASSWORD_FORM_RULES: FormRules<Login.LoginPasswordReqData> = {
  username: [{ required: true, message: '请输入账户名', trigger: 'blur' }],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 4, max: 16, message: '密码4-16位', trigger: 'blur' },
  ],
}

/**
 * 手机号登录表单规则
 */
export const PHONE_FORM_RULES: FormRules<Login.LoginMobileReqData> = {
  mobile: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: PHONE_REG, message: '请输入正确的手机号', trigger: 'blur' },
  ],
  captchaCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码不符', trigger: 'blur' },
  ],
}
