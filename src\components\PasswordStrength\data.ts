/**
 * 密码弱中强规则：
 * 1：弱密码：单纯某两种组成；如：333AAA 222sss
 * 2：中密码：三种混合长度低于等于8位
 * 3：强密码：三种混合长度超过8位
 */

import { HAS_LETTER_REG, HAS_NUMBER_REG, HAS_UNDERLINE_REG } from '@2030/utils'

export type PasswordStrength = 'weak' | 'medium' | 'strong' | ''

export function checkPasswordStrength(password: string): PasswordStrength {
  if (!password)
    return ''

  let score = 0

  // 包含数字
  if (HAS_NUMBER_REG.test(password))
    score++

  // 包含字母
  if (HAS_LETTER_REG.test(password))
    score++

  // 包含下划线
  if (HAS_UNDERLINE_REG.test(password))
    score++

  if (score <= 2)
    return 'weak'
  if (score === 3 && password.length <= 8)
    return 'medium'
  return 'strong'
}
