{"prettier.enable": false, "editor.formatOnSave": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "always", "source.organizeImports": "never"}, "eslint.rules.customizations": [{"rule": "style/*", "severity": "off", "fixable": true}, {"rule": "format/*", "severity": "off", "fixable": true}, {"rule": "*-indent", "severity": "off", "fixable": true}, {"rule": "*-spacing", "severity": "off", "fixable": true}, {"rule": "*-spaces", "severity": "off", "fixable": true}, {"rule": "*-order", "severity": "off", "fixable": true}, {"rule": "*-dangle", "severity": "off", "fixable": true}, {"rule": "*-newline", "severity": "off", "fixable": true}, {"rule": "*quotes", "severity": "off", "fixable": true}, {"rule": "*semi", "severity": "off", "fixable": true}], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "html", "markdown", "json", "jsonc", "yaml", "toml", "xml", "gql", "graphql", "astro", "css", "less", "scss", "pcss", "postcss"], "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/CVS": true, "**/node_modules": true}, "cSpell.words": ["ampproject", "astro", "Attributify", "Autosize", "avue", "brotli", "<PERSON>r", "Complexpass", "composables", "conventionalcommits", "cursorignore", "Destructurable", "dotenv", "dprint", "esbuild", "eslintcache", "filecontent", "Fullscreen", "Gamepad", "Geolocation", "iconfont", "iconify", "iiotplatform", "imgs", "INDEXEDDB", "infile", "intlify", "irecircle", "jscoverage", "jspm", "kooriookami", "localforage", "LOCALSTORAGE", "moderndash", "msword", "nocheck", "nprogress", "nuxt", "ox<PERSON>", "pausable", "persistedstate", "pids", "pinia", "preflights", "recircle", "scroller", "shang<PERSON>an", "<PERSON><PERSON>", "slidev", "smallwei", "Triggerable", "typecheck", "typegen", "unocss", "unplugin", "viewerjs", "VITE", "vite<PERSON><PERSON>", "vitepress", "vueuse", "wscript", "Wuzhong", "YESORNO"], "i18n-ally.localesPaths": ["src/locales"], "oxc_language_server.configPath": ".eslint.config.js", "i18n-ally.sourceLanguage": "en", "i18n-ally.keystyle": "nested", "npm-scripts.showStartNotification": false}