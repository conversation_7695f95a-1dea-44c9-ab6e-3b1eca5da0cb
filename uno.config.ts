import { defineConfig, presetAttributify, presetUno } from 'unocss'

export default defineConfig({
  /** 预设 */
  presets: [
    // 默认预设
    // @example: <div class="bg-red-500 text-white">Hello World</div>
    presetUno(),
    // 属性化模式
    // @example: <div bg-red-500 text-white>Hello World</div>
    // @example: <div bg="red-500" text="white">Hello World</div>
    presetAttributify(),
  ],
  /** 主题 */
  theme: {
    colors: {
      sk: {
        // 主色
        primary: {
          DEFAULT: '#5180F3',
          light: '#E3EBFF',
        },
        // 文本色
        text: {
          deep: '#3D3D3D',
          DEFAULT: '#777777',
          light: '#A5A5A5',
        },
        // 状态色
        success: '#46C58E',
        warning: '#F6A54C',
        error: '#F0412F',
        // 辅助色
        E4: '#E4E4E4',
        F5: '#F5F5F5',
        background: '#F4F5FC',
        // 导航栏
        blueDeep: '#03317D',
        grayDeep: '#393D45',
      },
    },
  },
  // 定义全局css变量
  preflights: [
    {
      getCSS: ({ theme }: { theme: any }) => `
        :root {
          /* 覆盖element-plus默认样式 */
          --el-color-primary: ${theme.colors.sk.primary.DEFAULT};
          --el-color-success: ${theme.colors.sk.success};
          --el-color-warning: ${theme.colors.sk.warning};
          --el-color-error: ${theme.colors.sk.error};
          --el-color-border: ${theme.colors.sk.E4};
          --el-text-color-regular: ${theme.colors.sk.text.deep};

          /* 添加文本颜色变量 */
          --sk-primary-light: ${theme.colors.sk.primary.light};
          --sk-text-light: ${theme.colors.sk.text.light};
          --sk-text: ${theme.colors.sk.text.DEFAULT};
          --sk-text-deep: ${theme.colors.sk.text.deep};
          --sk-error: ${theme.colors.sk.error};
          --sk-warning: ${theme.colors.sk.warning};
          --sk-success: ${theme.colors.sk.success};
          --sk-E4: ${theme.colors.sk.E4};
          --sk-F5: ${theme.colors.sk.F5};
          --sk-background: ${theme.colors.sk.background};
          --sk-blue-deep: ${theme.colors.sk.blueDeep};
          --sk-gray-deep: ${theme.colors.sk.grayDeep};
        }
      `,
    },
  ],
  /** 内容 */
  content: {
    pipeline: {
      exclude: ['node_modules', 'dist', 'public', 'build', 'uno.config.ts'],
    },
  },
  /** 自定义规则 */
  rules: [
    // 添加 wh-[xxx] 支持
    [/^wh-\[(.+)\]$/, ([, d]) => ({ width: d, height: d })],
    // @example: 将 padding-15 转换为 padding: 15px
    ['padding-15', { padding: '15px' }],
    // 单行省略样式
    ['truncate', {
      'overflow': 'hidden',
      'text-overflow': 'ellipsis',
      'white-space': 'nowrap',
    }],
    // 支持行数限制（如两行省略）
    [/^line-clamp-(\d+)$/, ([, lines]) => ({
      'display': '-webkit-box',
      '-webkit-line-clamp': lines,
      '-webkit-box-orient': 'vertical',
      'overflow': 'hidden',
    })],
  ],
  /** 自定义快捷方式 */
  shortcuts: {
    'wh-full': 'w-full h-full',
    'flex-center': 'flex justify-center items-center',
    'flex-x-center': 'flex justify-center',
    'flex-y-center': 'flex items-center',
  },
  /** 自动完成 */
  autocomplete: {
    templates: [
      // 文本颜色提示
      'text-sk-<sk-color>',
      // 背景色提示
      'bg-sk-<sk-color>',
      // 边框颜色提示
      'border-sk-<sk-color>',
    ],
    shorthands: {
      'sk-color': '(primary|primary-light|text|text-deep|text-light|success|warning|error|background|E4|F5|blue-deep|gray-deep)',
    },
  },
})
