<script setup lang="ts">
import type { RegionRes } from '@/api/region/types'
import type { CascaderProps, ElCascader, ElUpload, FormInstance, FormRules, UploadRawFile } from 'element-plus'
import { getAccountInfo, updateAccountInfo, type UpdateAccountInfoReq } from '@/api/personal/base-setting'
import { getTreeRegion } from '@/api/region'
import { updateUserName } from '@/api/user'
import { REG } from '@/constant/reg'
import { useBeforeUpload } from '@/hooks/useBeforeUpload'

import { getToken } from '@/service/auth'

import { useUserStore } from '@/store/modules/user'

import { useDebounceFn } from '@vueuse/core'

export interface FileResponse {
  code: number
  data: FileInfo
  msg: null
}

export interface FileInfo {
  documentNo: string
  documentName: string
  documentVersion: null
  documentLength: string
  documentUrl: string
  uploadTime: number
}

defineOptions({
  name: 'BasicSetting',
})

const { PHONE } = REG

const baseInfo = ref({}) as Ref<UpdateAccountInfoReq>
const rules = reactive<FormRules<typeof baseInfo>>({
  telephone: [{ pattern: PHONE, message: '请输入正确的手机号码', trigger: 'blur' }],
})

const { VITE_OSS_API } = import.meta.env

const { updateUserInfo } = useUserStore()

const formRef = useTemplateRef<FormInstance>('formRef')

const uploadRef = useTemplateRef<InstanceType<typeof ElUpload>>('uploadRef')
const fileList = ref([])
const accept = '.jpg,.jpeg,.png'
const handleBeforeUpload = (rawFile: UploadRawFile) => useBeforeUpload(rawFile, { isAccept: true, accept, acceptMsg: '请上传符合格式的文件类型', isLimitSize: true, limitSize: 10, limitMsg: '请上传符合大小的文件' })
function handleUploadSuccess(response: FileResponse) {
  const { code, data } = response
  if (code === 0) {
    baseInfo.value.avatar = data.documentUrl
    uploadRef.value?.clearFiles()
  }
}

const provinceCity = ref<string[]>([])
const provinceLabelModel = reactive({
  countryName: '',
  provinceName: '',
  cityName: '',
  areaName: '',
})

const cascaderInstance = useTemplateRef<InstanceType<typeof ElCascader>>('provinceRef')

const provinceCityProps: CascaderProps = {
  lazy: true,
  async lazyLoad(node, resolve) {
    const { level } = node
    const data: RegionRes[] = await getTreeRegion(level === 1 ? (node.value as string) : void 0)
    const nodes = data.map(item => ({
      value: item.id,
      label: item.regionName,
      leaf: level >= 1,
    }))
    resolve(nodes)
  },
}

watchEffect(() => {
  baseInfo.value.province = provinceCity.value ? provinceCity.value[0] : ''
  baseInfo.value.city = provinceCity.value ? provinceCity.value[1] : ''
})

const isDisabled = ref(true)
let isInitData = false
watch(
  () => baseInfo.value,
  () => {
    if (isInitData) {
      isDisabled.value = false
    }
  },
  { deep: true },
)

function getProvinceLabel() {
  const nodes = cascaderInstance.value?.getCheckedNodes(false)
  const { pathLabels } = nodes?.[0] ?? {}
  Object.assign(provinceLabelModel, {
    provinceName: pathLabels?.[0] ?? '',
    cityName: pathLabels?.[1] ?? '',
  })
}

async function handleSave() {
  try {
    const valid = await formRef.value?.validate()
    if (!valid)
      return

    getProvinceLabel()

    // 合并所有需要更新的信息
    const updateData = {
      ...baseInfo.value,
      ...provinceLabelModel,
    }
    // 并行执行两个更新操作
    await Promise.all([
      updateAccountInfo(updateData),
      updateUserInfo({
        ...provinceLabelModel,
        ...unref(baseInfo.value),
      }),
    ])

    $message.success('保存成功')
  } catch (error) {
    console.error('保存失败:', error)
  }
}

async function getAccountInfoApi() {
  const data = await getAccountInfo()
  delete data.id
  delete data.area
  const { countryName = '', provinceName = '', cityName = '', areaName = '', ...rest } = data
  baseInfo.value = rest
  const { province, city } = baseInfo.value
  if (province && city) {
    provinceCity.value = [province, city]
  }
  Object.assign(provinceLabelModel, {
    countryName,
    provinceName,
    cityName,
    areaName,
  })
  nextTick(() => {
    isInitData = true
  })
}

const isModifyAccount = ref(false)
const accountRef = ref<FormInstance>()
const accountModel = reactive({
  username: '',
})

const footBtnConfig = reactive({
  isConfirmBtnLoading: false,
})

function openAccountModal() {
  accountModel.username = baseInfo.value.username || ''
  isModifyAccount.value = true
}

function validateAccountForm(): Promise<{ success: boolean }> {
  return new Promise((resolve) => {
    accountRef.value?.validate((valid) => {
      resolve({
        success: !!valid,
      })
    })
  })
}

async function onBeforeOpen() {
  await nextTick(() => {
    accountRef.value?.clearValidate()
  })
}

function validateUserName(value: string, callback: (error?: string | Error) => void) {
  if (!value)
    return callback()
  const reg = /^(?![a-z]+$|\d+$|_+$)\w+$/i
  if (!reg.test(value)) {
    return callback(new Error('字母/数字/下划线，至少包含2种'))
  }
  return callback()
}
const onConfirm = useDebounceFn(async () => {
  const { success } = await validateAccountForm()
  if (!success) {
    return
  }
  const res = await updateUserName({
    userName: accountModel.username,
  })
  if (!res)
    return
  $message.success('修改成功')
  isModifyAccount.value = false
  updateUserInfo({ username: accountModel.username })
  getAccountInfoApi()
}, 300)

onBeforeMount(() => {
  getAccountInfoApi()
})
</script>

<template>
  <div class="basic-setting personal-page">
    <span class="personal-page-title">基本设置</span>
    <el-form
      ref="formRef"
      :model="baseInfo"
      :rules="rules"
      label-position="top"
    >
      <el-form-item class="avatar-upload h-full w-full">
        <el-upload
          ref="uploadRef"
          :action="VITE_OSS_API"
          :headers="{ authorization: getToken() }"
          :file-list="fileList"
          type="picture-card"
          :show-file-list="false"
          :accept="accept"
          :limit="1"
          :before-upload="handleBeforeUpload"
          :on-success="handleUploadSuccess"
        >
          <el-avatar :src="baseInfo.avatar" />
          <div class="h-20 flex flex-col justify-between">
            <p class="flex items-center justify-start text-nowrap font-bold" @click.stop="openAccountModal">
              <span>{{ baseInfo?.username || '' }}</span>
              <el-icon class="ml-[8px]" size="24">
                <Edit />
              </el-icon>
            </p>
            <el-button class="upload-btn">
              <span class="iconfont icon-shangchuan" />
              上传头像
            </el-button>
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item label="昵称">
        <el-input
          v-model="baseInfo.nickname"
          placeholder="请输入"
          :maxlength="20"
          clearable
        />
      </el-form-item>
      <el-form-item
        label="电子邮箱"
        prop="email"
      >
        <el-input
          v-model="baseInfo.email"
          placeholder="请输入"
          :maxlength="30"
          clearable
        />
      </el-form-item>
      <el-form-item
        label="个人简介"
      >
        <el-input
          v-model="baseInfo.intro"
          placeholder="请输入"
          :maxlength="20"
          clearable
        />
      </el-form-item>
      <el-form-item
        label="联系电话"
        prop="telephone"
      >
        <el-input
          v-model="baseInfo.telephone"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      <el-form-item label="所在省市">
        <el-cascader
          ref="provinceRef"
          v-model="provinceCity"
          :props="provinceCityProps"
          placeholder="请选择"
          clearable
        />
      </el-form-item>
      <el-form-item
        class="w-full"
        label="详细地址"
      >
        <el-input
          v-model="baseInfo.street"
          placeholder="请输入"
          :maxlength="30"
          clearable
        />
      </el-form-item>
      <el-form-item class="mb-0">
        <el-button
          class="w-83"
          type="primary"
          :disabled="isDisabled"
          @click="handleSave"
        >
          保存
        </el-button>
      </el-form-item>
    </el-form>
    <SkTipDialog
      v-model="isModifyAccount"
      head-title="修改账户名"
      :is-confirm-tip="false"
      :foot-btn-config="footBtnConfig"
      @cancel="isModifyAccount = false"
      @confirm="onConfirm"
      @open="onBeforeOpen"
    >
      <el-form ref="accountRef" :model="accountModel">
        <el-form-item
          label="账户名"
          prop="username"
          :rules="[
            { required: true, message: '请输入用户名', trigger: 'blur' },
            { validator: (_, value, callback) => validateUserName(value, callback), trigger: ['blur', 'change'] },
          ]"
        >
          <el-input v-model="accountModel.username" />
        </el-form-item>
      </el-form>
    </SkTipDialog>
  </div>
</template>

<style lang="scss" scoped>
.basic-setting {
  :deep(.el-form) {
    display: flex;
    flex-wrap: wrap;

    .el-form-item {
      width: 350px;
      margin: 0 120px 30px 0;

      .el-form-item__label {
        margin-bottom: 5px;
      }

      .el-form-item__content {
        > .el-input {
          height: 34px;
        }

        .el-cascader {
          width: 100%;
        }
      }
    }

    .avatar-upload {
      > div {
        width: 200px;
      }

      .el-upload {
        display: flex;
        justify-content: flex-start;
        align-items: flex-end;

        .el-avatar {
          width: 80px;
          height: 80px;
          margin-right: 20px;
        }

        .upload-btn {
          width: 97px;
          height: 30px;
          background-color: #f5f5f5;
          border: none;
          border-radius: 5px;

          .iconfont {
            font-size: 18px;
          }
        }
      }
    }

    .w-full {
      width: 100%;
    }

    .w-83 {
      width: 83px;
    }

    .mb-0 {
      margin-bottom: 0;
    }
  }
}
</style>
