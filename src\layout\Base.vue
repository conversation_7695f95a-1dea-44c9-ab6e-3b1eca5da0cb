<script setup lang="ts">
import LOGO_ALPHA from '@/assets/imgs/logo-alpha.png'
import { getToken } from '@/service/auth'

const route = useRoute()
const router = useRouter()
const { logout, userInfo } = useUserStore()

const setUserName = computed(() => {
  return userInfo?.realName ? userInfo.name ?? '' : userInfo?.mobile ?? ''
})

function getAvatarMenu(): { label: string, handle: () => void }[] {
  return getToken()
    ? [{
        label: '安全退出',
        handle: () => {
          logout()
        },
      }]
    : []
}

/**
 * 计算当前背景色
 */
const bgColor = computed(() => {
  return route.name === 'Home' ? 'bg-sk-blue-deep' : 'bg-sk-gray-deep'
})

/**
 * 跳转首页
 */
function goHome() {
  router.push({ name: 'Home' })
}
</script>

<template>
  <div wh-full flex flex-col select-none class="sk-section">
    <div sticky top-0 z-50>
      <div :class="[bgColor]" h-12.5 w-full flex justify-between p-x-4 text-white>
        <div flex-y-center cursor-pointer @click="goHome">
          <img :src="LOGO_ALPHA" alt="logo" h-7 w-15>
          <span ml-2 text-base>江西瑟壳数字科技有限公司</span>
        </div>
        <div flex-y-center>
          <el-dropdown trigger="click">
            <div class="ml-[16px] flex-y-center">
              <!-- <div wh-10 bg-sk-f5 flex-center bg-white>
              <img v-if="userInfo?.avatar" :src="userInfo.avatar" alt="avatar" h-7.5 w-7.5 rounded-full>
                <i-ep:avatar v-else h-7.5 w-7.5 text-sk-text />
              </div> -->
              <el-avatar :size="30" :src="userInfo?.avatar" />
              <p v-if="userInfo?.username" class="ml-[8px] text-white">
                {{ setUserName }}
              </p>
              <i-ep:caret-bottom class="ml-[16px] h-5.5 w-5.5 text-white" />
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="item in getAvatarMenu()" :key="item.label" @click="item.handle">
                  {{ item.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
    <div flex-1 overflow-auto>
      <router-view />
    </div>
  </div>
</template>
