<script setup lang="ts">
export interface Prop {
  /** 文件列表--优先级高于fileInfo */
  fileList?: Recordable[]
  /** 单个文件信息 */
  fileInfo?: Recordable
  /** 是否显示下载--默认显示 */
  isShowDownload?: boolean
  /** 是否显示删除--默认不显示 */
  isShowDelete?: boolean
  /** 文件名url字段名--默认documentUrl */
  fileUrl?: string
  /** 文件名name字段名--默认documentName */
  fileName?: string
}

const props = withDefaults(defineProps<Prop>(), {
  isShowDownload: true,
  isShowDelete: false,
  fileUrl: 'documentUrl',
  fileName: 'documentName',
})

const emit = defineEmits<{
  remove: [file: Recordable]
}>()

function downloadFile(fileInfo: Recordable) {
  const x = new window.XMLHttpRequest()
  x.open('GET', fileInfo[props.fileUrl], true)
  x.responseType = 'blob'
  x.onload = () => {
    const url = window.URL.createObjectURL(x.response)
    const a = document.createElement('a')
    a.href = url
    a.target = '_blank'
    a.download = fileInfo[props.fileName]
    a.style.display = 'none'
    document.body.append(a)
    a.click()
  }
  x.send()
}

function handleDelete(fileInfo: Recordable) {
  emit('remove', fileInfo)
}
function getFilePrefix(fileName = '', isRetainSpot = true) {
  return fileName.replace(/\.\w+$/, `${isRetainSpot ? '.' : ''}`)
}
function getFileSuffix(fileName = '') {
  return fileName.replace(/.+\./, '')
}
</script>

<template>
  <ul
    v-if="fileList?.length"
    style="margin: 0 10px 0 -10px"
  >
    <li
      v-for="(item, index) in fileList"
      :key="index"
      class="sk-annex"
    >
      <span
        class="file-name"
        :title="item?.[fileName]"
      >
        {{ getFilePrefix(item?.[fileName]) }}
      </span>
      <span
        class="file-suffix"
        :title="item?.[fileName]"
      >
        {{ getFileSuffix(item?.[fileName]) }}
      </span>
      <span
        v-if="isShowDownload"
        class="download"
        @click="downloadFile(item)"
      >
        下载
      </span>
      <span
        v-if="isShowDelete"
        class="delete"
        @click="handleDelete(item)"
      >
        删除
      </span>
    </li>
  </ul>
  <div
    v-else-if="fileInfo?.[fileUrl]"
    class="sk-annex"
  >
    <span
      class="file-name"
      :title="fileInfo?.[fileName]"
    >
      {{ getFilePrefix(fileInfo?.[fileName]) }}
    </span>
    <span
      class="file-suffix"
      :title="fileInfo?.[fileName]"
    >
      {{ getFileSuffix(fileInfo?.[fileName]) }}
    </span>
    <span
      v-if="isShowDownload"
      class="download"
      @click="downloadFile(fileInfo)"
    >
      下载
    </span>
    <span
      v-if="isShowDelete"
      class="delete"
      @click="handleDelete(fileInfo)"
    >
      删除
    </span>
  </div>
</template>

<style lang="scss" scoped>
.sk-annex {
  display: flex;
  align-items: center;

  width: 450px;
  padding: 0 10px;
  box-sizing: border-box;

  height: 20px;
  font-size: 12px;
  color: #3d3d3d;

  .file-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
  }

  .download,
  .delete {
    display: none;
    width: 44px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    margin-left: auto;
    cursor: pointer;
  }

  .download:hover,
  .delete:hover {
    background-color: #e4e4e4;
  }
}

.sk-annex:hover {
  background-color: #f3f3f3;

  .file-name,
  .file-suffix {
    color: #5180f3;
  }

  .download,
  .delete {
    display: inline-block;
  }
}
</style>
