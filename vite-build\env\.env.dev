# 开发环境（命名必须以 VITE_ 开头）

## 项目名称
VITE_APP_NAME = Recircle
## 项目描述
VITE_APP_DESC =
## 项目公共基础路径,即开发环境地址前缀（一般 '/'，'./' 都可以）
VITE_BASE_URL = /
## API地址，优先顺序：_app.global.js > env
VITE_BASE_API = http://dev-tenant.irecircle.com/tenant-api
# VITE_BASE_API = http://***************:18090/tenant-api
## OSS地址，优先顺序：_app.global.js > env
VITE_OSS_API = http://dev-tenant.irecircle.com/common-api/infra/file/upload
## 租户跳转地址
VITE_TENANT_REDIRECT_URL = http://dev-tenant.irecircle.com/workbench
## 路由模式
## - "history": 历史模式
## - "hash": 哈希模式
VITE_ROUTE_MODE = history
## 构建压缩类型
## - "gzip": gz
## - "brotli": br
## - "gzip,brotli": gz+br
## - "none": 不压缩
VITE_BUILD_COMPRESS = gzip
## 是否删除打印信息
## - "Y"/"true": true
## - "N"/"false": false
VITE_DROP_CONSOLE = N
