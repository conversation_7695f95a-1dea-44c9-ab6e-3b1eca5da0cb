<script setup lang="ts">
import type * as Login from '@/api/user/types'
import type { FormInstance } from 'element-plus'
import { register, sendCode } from '@/api/user'
import { LoginTabEnum } from '@/enum'
import { useCountDown } from '@/hooks/useCountDown'
import { cryptoJS, PHONE_REG } from '@2030/utils'
import { clone } from 'xe-utils'
import { useRules } from '../../hooks/useSignUpRules'

const formParams = reactive<Login.RegisterReqData>({
  username: '',
  mobile: '',
  captchaCode: '',
  password: '',
  rePassword: '',
  sourceType: 1,
})

const { countdown, isCounting, handleSendCode } = useCountDown()

const formRef = useTemplateRef<FormInstance>('formRef')
const { formRules, isMobileValid } = useRules(formParams)
const verifyBtnDisabled = computed(() => isCounting.value || !PHONE_REG.test(formParams.mobile))
const currComponent = inject<Ref<'signUpForm' | 'signUpResult'>>('currComponent')
const userName = inject<Ref<string>>('userName')
const { btnLoading } = useBtnLoading()
async function handleRegister() {
  if (!formRef.value)
    return
  try {
    const validated = await formRef.value.validate()
    if (validated) {
      btnLoading.value = true
      try {
        const cloneFormParams = { ...clone(formParams, true), password: cryptoJS.encryptMD5(formParams.password), rePassword: cryptoJS.encryptMD5(formParams.rePassword) }
        const account = await register(cloneFormParams)
        if (account) {
          $message.success('注册成功')
          userName!.value = account
          currComponent!.value = 'signUpResult'
        }
      } finally {
        btnLoading.value = false
      }
    }
  } catch {
    // SECTION - 校验失败额外处理
  }
}

/**
 * 获取验证码
 */
async function handleGetCode() {
  if (!isMobileValid.value) {
    return $message.error('请输入正确的手机号')
  }
  const status = await sendCode({ mobile: formParams.mobile })
  if (status) {
    await handleSendCode()
  }
}

/**
 * 切换登录
 */
const currTab = inject<Ref<LoginTab>>('currTab')
function handleChangeSignIn() {
  currTab!.value = LoginTabEnum.SIGN_IN
}

/**
 * 监听密码输入框的验证状态
 */
let passwordFormItem: Element | null = null
const passwordError = ref(false)
const observer = new MutationObserver(() => {
  const errorElement = passwordFormItem?.querySelector('.el-form-item__error')
  passwordError.value = !!errorElement
})
onMounted(() => {
  passwordFormItem = document.querySelectorAll('.el-form-item')[1]
  if (passwordFormItem) {
    observer.observe(passwordFormItem, {
      childList: true,
      subtree: true,
    })
  }
})
onBeforeUnmount(() => {
  observer.disconnect()
})
</script>

<template>
  <div bg="[rgba(255,255,255,0.95)]" w-500px rounded-5px px-12.5 pb-5 pt-7.5 shadow-lg>
    <div mb-5 text-xl text-sk-text-deep>
      注册
    </div>
    <el-form ref="formRef" :model="formParams" label-width="96" hide-required-asterisk :rules="formRules">
      <el-form-item label="手机号码" prop="mobile">
        <el-input v-model="formParams.mobile" placeholder="请输入" clearable maxlength="11">
          <template #prefix>
            <!-- 可选 +86 -->
            <span>+86</span>
          </template>
        </el-input>
      </el-form-item>
      <div class="verify-code-wrap flex justify-between">
        <el-form-item label="验证码" prop="captchaCode">
          <el-input v-model.trim="formParams.captchaCode" maxlength="6" placeholder="请输入验证码" clearable />
        </el-form-item>
        <el-button :type="verifyBtnDisabled ? 'default' : 'primary'" :disabled="verifyBtnDisabled" plain class="send-code-btn" @click="handleGetCode">
          {{ isCounting ? `${countdown}s重新获取` : '获取验证码' }}
        </el-button>
      </div>
      <el-form-item label="密码" prop="password">
        <el-input v-model.trim="formParams.password" type="password" placeholder="请输入" minlength="6" maxlength="20" clearable show-password />
      </el-form-item>
      <PasswordStrength :password="formParams.password" :offset-top="passwordError ? '0px' : '-15px'" offset-left="70px" />
      <el-form-item label="再次输入密码" prop="rePassword" clearable>
        <el-input v-model.trim="formParams.rePassword" type="password" placeholder="请输入" show-password />
      </el-form-item>
    </el-form>

    <el-button type="primary" mt-10 w-full :loading="btnLoading" @click="handleRegister">
      注册
    </el-button>

    <div mt-5 flex justify-end text-xs>
      <span cursor-pointer text-sk-primary @click="handleChangeSignIn">使用已有账号登录</span>
    </div>
  </div>
</template>

<style scoped>
@import '../index.css';
</style>
