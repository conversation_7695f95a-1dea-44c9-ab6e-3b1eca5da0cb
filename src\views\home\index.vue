<script setup lang="ts">
import HOME_BANNER_1 from '@/assets/imgs/home/<USER>'
import HOME_BANNER_2 from '@/assets/imgs/home/<USER>'
import PROJECT_BG_0 from '@/assets/imgs/home/<USER>'
import PROJECT_BG_1 from '@/assets/imgs/home/<USER>'
import PROJECT_BG_2 from '@/assets/imgs/home/<USER>'
import PROJECT_BG_3 from '@/assets/imgs/home/<USER>'
import PROJECT_BG_4 from '@/assets/imgs/home/<USER>'
import { AppConfig } from '@/config'

const bannerList = [HOME_BANNER_1, HOME_BANNER_2]
const projectList = [
  { bg: PROJECT_BG_0, title: 'SRM采购管理系统' },
  { bg: PROJECT_BG_1, title: '合同全生命周期管理' },
  { bg: PROJECT_BG_2, title: '供应商全生命周期管理' },
  { bg: PROJECT_BG_3, title: '订单数字化管理' },
  { bg: PROJECT_BG_4, title: '财务智能化管理系统' },
]
const { COPYRIGHT_NUMBER } = AppConfig

function handleCopyrightClick() {
  window.open(AppConfig.COPYRIGHT_URL)
}
</script>

<template>
  <div select-none>
    <el-carousel autoplay motion-blur h-650px w-full>
      <el-carousel-item v-for="(item, index) in bannerList" :key="index">
        <img :src="item" lazy h-full w-full>
      </el-carousel-item>
    </el-carousel>
    <div mb-286px mt-87px flex-x-center>
      <section v-for="(item, index) in projectList" :key="index" relative m-x-2.5 h-100px w-224px flex-center>
        <p font-bold>
          {{ item.title }}
        </p>
        <img :src="item.bg" absolute left-0 top-0 z-1 h-full w-full object-cover>
      </section>
    </div>
    <section h-10 flex-center bg-sk-blue-deep text-white>
      <p cursor-pointer @click="handleCopyrightClick">
        {{ COPYRIGHT_NUMBER }}
      </p>
    </section>
  </div>
</template>

<style scoped>
:deep(.el-carousel__container) {
  height: 100%;
}
</style>
