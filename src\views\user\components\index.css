:deep(.el-tabs) {
  .el-tabs__header {
    margin-bottom: 0;
  }
  .el-tabs__nav-wrap {
    &::after {
      height: 0;
    }
  }
  .el-tabs__item {
    font-size: 20px;
    color: var(--sk-text-light);
    &.is-active {
      font-size: 22px;
      font-weight: 600;
      color: var(--sk-text-deep);
    }
  }
}

:deep(.el-input) {
  & > .el-input__wrapper {
    height: 50px;
    background-color: var(--sk-F5);
    box-shadow: none;
  }
}

:deep(.el-form-item) {
  display: flex;
  align-items: center;
}

.verify-code-wrap {
  .el-form-item {
    flex: 1;
    margin-right: 20px;
  }
  .send-code-btn {
    width: 99px;
    background-color: white;
    &:hover {
      background-color: var(--el-color-primary);
    }
    &[disabled] {
      color: var(--sk-E4);
      &:hover {
        background-color: white;
      }
    }
  }
}

.el-button {
  height: 50px;
}
