<script setup lang="ts">
import { ElMessage } from 'element-plus'

interface Prop {
  spareEmail: string | null
}

const props = defineProps<Prop>()

const email = ref('')
watchEffect(() => {
  email.value = props.spareEmail ?? ''
})

function handleEdit() {
  ElMessage.warning('该功能后续开发')
}
</script>

<template>
  <div>
    <div class="content">
      <span class="title">备用邮箱</span>
      <span class="info">
        已绑定邮箱
        <span class="ml-20">{{ email }}</span>
      </span>
    </div>
    <el-button
      type="primary"
      link
      @click="handleEdit"
    >
      修改
    </el-button>
  </div>
</template>

<style lang="scss" scoped>
.ml-20 {
  margin-left: 20px;
}
</style>
