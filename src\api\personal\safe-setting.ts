import request from '@/service/axios'

/**
 * @file 个人设置-安全设置相关API
 */

/** 密码强度类型 */
export type PasswordStrength = 1 | 2 | 3

/** 安全信息类型 */
export interface SafeInfo {
  /** 密码强度（1-强；2-中；3-弱） */
  passwordStrength: PasswordStrength
  /** 脱敏后的密保手机 */
  desensitizationMobile: string
  /** 脱敏后的密保邮箱 */
  desensitizationEmail: string
}

/** 安全设置响应数据类型 */
export interface SafeSettingRes extends SafeInfo {
  id: string
}

/** 修改密保手机请求参数类型 */
export interface UpdateMobileReq {
  /** 手机号码 */
  mobile: string
  /** 验证码 */
  code: string
}

/** 修改账户密码请求参数类型 */
export interface UpdatePasswordReq {
  /** 密码 */
  password: string
  /** 确认新密码 */
  rePassword: string
}

/** 获取安全设置信息 */
export const getSafeSetting = () => request.get<SafeSettingRes>({ url: '/user-center/user/safe-setting' })

/** 获取短信验证码 */
export const getSmsCode = (mobile: string) => request.post<boolean>({ url: '/user-center/user/send-code', data: { mobile } })

/** 修改密保手机 */
export const updateMobile = (data: UpdateMobileReq) => request.put<boolean>({ url: '/user-center/user/update-mobile', data })

/** 修改账户密码 */
export const updatePassword = (data: UpdatePasswordReq) => request.put<boolean>({ url: '/user-center/user/update-password', data })
