<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import { forgetPassword, sendCodeForForgetPassword } from '@/api/user'
import { useCountDown } from '@/hooks/useCountDown'
import { PHONE_REG } from '@2030/utils'
import { FORM_RULES } from './data'

const formParams = reactive({
  username: '',
  mobile: '',
  captchaCode: '',
})

const currTab = inject<Ref<'signIn' | 'signUp'>>('currTab')
function handleChangeSignIn() {
  currTab!.value = 'signIn'
}

const { btnLoading } = useBtnLoading()
const forgetFormRef = useTemplateRef<FormInstance>('forgetFormRef')
async function handleForgetPassword() {
  if (!forgetFormRef.value)
    return
  try {
    const validated = await forgetFormRef.value.validate()
    if (validated) {
      btnLoading.value = true
      try {
        const isSuccess = await forgetPassword(formParams)
        if (isSuccess) {
          $message.success('您的新密码已发送至新手机号,请注意查收')
          handleChangeSignIn()
        }
      } finally {
        btnLoading.value = false
      }
    }
  } catch {
    // SECTION - 校验失败额外处理
  }
}

const { countdown, isCounting, handleSendCode } = useCountDown()
const verifyBtnDisabled = computed(() => isCounting.value || !PHONE_REG.test(formParams.mobile))
/**
 * 获取验证码
 */
async function handleGetCode() {
  if (verifyBtnDisabled.value) {
    return $message.error('请输入正确的手机号')
  }
  const status = await sendCodeForForgetPassword({ mobile: formParams.mobile })
  if (status) {
    await handleSendCode()
  }
}
</script>

<template>
  <div bg="[rgba(255,255,255,0.95)]" w-500px rounded-5px px-12.5 pb-5 pt-7.5 shadow-lg>
    <div mb-5 text-xl text-sk-text-deep>
      <span flex cursor-pointer items-center @click="handleChangeSignIn">
        <i-ep:back /> <span ml-2.5>忘记密码</span>
      </span>
    </div>
    <el-form ref="forgetFormRef" label-width="80" :model="formParams" :rules="FORM_RULES">
      <el-form-item label="账户名" prop="username">
        <el-input v-model="formParams.username" maxlength="30" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input v-model="formParams.mobile" placeholder="请输入" clearable />
      </el-form-item>
      <div class="verify-code-wrap flex justify-between">
        <el-form-item label="验证码" prop="captchaCode">
          <el-input v-model.trim="formParams.captchaCode" maxlength="6" placeholder="请输入验证码" clearable />
        </el-form-item>
        <el-button :type="verifyBtnDisabled ? 'default' : 'primary'" :disabled="verifyBtnDisabled" plain class="send-code-btn" @click="handleGetCode">
          {{ isCounting ? `${countdown}s重新获取` : '获取验证码' }}
        </el-button>
      </div>
    </el-form>

    <div mt-10 flex justify-between>
      <el-button type="default" plain w-190px @click="handleChangeSignIn">
        取消
      </el-button>
      <el-button type="primary" w-190px :loading="btnLoading" @click="handleForgetPassword">
        确定
      </el-button>
    </div>
  </div>
</template>

<style scoped>
@import '../index.css';
</style>
