# 定义安装依赖的作业
install_dependencies_for_test:
  stage: install
  tags:
    - pnpm
  script:
    - pnpm install
  only:
    - test
  cache:
    key: ${CI_PROJECT_NAME}
    paths:
      - node_modules/
      - packages/utils/node_modules/

# 构建项目
build_main_project_for_test:
  stage: build
  tags:
    - pnpm
  dependencies:
    - install_dependencies_for_test
  needs:
    - install_dependencies_for_test
  script:
    - pnpm run build:test # 执行构建命令
  artifacts:
    paths:
      - dist/ # 保存构建后的输出
    expire_in: 1 week # 构建产物保存一周
  only:
    - test
  cache:
    key: ${CI_PROJECT_NAME}
    paths:
      - node_modules/
      - packages/utils/node_modules/

# 构建镜像
build_main_image_for_test:
  stage: build_img
  tags:
    - host-docker-builder
  dependencies:
    - build_main_project_for_test
  needs:
    - build_main_project_for_test
  before_script:
    - echo "登录docker镜像仓库 $DOCKER_REGISTRY"
    - echo "$DOCKER_REGISTRY_PASS" | docker login $DOCKER_REGISTRY --username $DOCKER_REGISTRY_USER --password-stdin
  script:
    - docker build -t $DOCKER_REGISTRY/recircle-industry-platform/$IMAGE_NAME:test .
    - docker push $DOCKER_REGISTRY/recircle-industry-platform/$IMAGE_NAME:test
  only:
    - test

# 部署上K8S
deploy_main_k8s_for_test:
  stage: deploy
  tags:
    - kube-deployer
  dependencies:
    - build_main_image_for_test
  needs:
    - build_main_image_for_test
  script:
    - kubectl patch deployment $IMAGE_NAME-v1 --context=<EMAIL> -n recircle-industry-platform-test -p "{\"spec\":{\"template\":{\"metadata\":{\"annotations\":{\"kubectl.kubernetes.io/restartedAt\":\"$(date +%Y-%m-%dT%H:%M:%S)\"}}}}}"
  only:
    - test
