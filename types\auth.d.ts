declare namespace Auth {
  interface UserInfo {
    // 用户id
    id: string
    // 用户名
    username: string
    // 邮箱
    email?: string
    // 手机号
    mobile?: string
    // 用户头像
    avatar?: string
    // 用户状态
    status?: string
    // 用户昵称
    nickname?: string
    // 自动登录
    autoLogin?: boolean
  }

  // 角色类型
  // super: 超级管理员
  // admin: 管理员
  // user: 普通用户
  // guest: 游客
  type RoleType = 'super' | 'admin' | 'user' | 'guest'
}
