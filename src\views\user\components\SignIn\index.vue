<script setup lang="ts">
import type { CheckboxValueType, FormInstance } from 'element-plus'
import { accountIcon, passwordIcon, verifyCodeIcon } from '@/assets/base64'
import { LoginTabEnum, LoginTypeEnum } from '@/enum'
import { handleToWebsite } from '@/utils'
import { PASSWORD_FORM_RULES, PHONE_FORM_RULES, TABS } from './data'

const activeTab = ref<LoginTypeEnum>(LoginTypeEnum.PASSWORD)

/**
 * 切换注册
 */
const currTab = inject<Ref<LoginTab>>('currTab')
function handleChangeSignUp() {
  currTab!.value = LoginTabEnum.SIGN_UP
}
/**
 * 切换忘记密码
 */
function handleChangeForgotPassword() {
  currTab!.value = LoginTabEnum.FORGOT_PASSWORD
}

/**
 * 底部链接 TODO: v0.4 暂时去除
 * TODO: v0.4 暂时去除
 */
// const router = useRouter()
// function handleLinkClick(routerName: string) {
//   router.push(routerName)
// }

/**
 * 表单Hook
 */
const passwordFormRef = useTemplateRef<FormInstance>('passwordFormRef')
const mobileFormRef = useTemplateRef<FormInstance>('mobileFormRef')
const {
  passwordFormParams,
  mobileFormParams,
  verifyBtnDisabled,
  countdown,
  isCounting,
  btnLoading,
  loginPassword,
  loginPhone,
  handleGetCode,
} = useSignIn(passwordFormRef as Ref<FormInstance>, mobileFormRef as Ref<FormInstance>)

/**
 * 自动登录设置
 */
const { setAutoLogin } = useUserStore()

/**
 * 登录
 */
function login() {
  if (activeTab.value === LoginTypeEnum.PASSWORD)
    loginPassword()
  else
    loginPhone()
}
</script>

<template>
  <div bg="[rgba(255,255,255,0.95)]" h-fit w-500px rounded-5px px-12.5 pb-5 pt-12.5 shadow-lg>
    <el-tabs v-model="activeTab">
      <el-tab-pane :label="TABS[0].label" :name="TABS[0].name">
        <el-form ref="passwordFormRef" :model="passwordFormParams" :rules="PASSWORD_FORM_RULES" clearable mt-43px>
          <el-form-item prop="username">
            <el-input v-model="passwordFormParams.username" placeholder="请输入账户名" maxlength="30" clearable>
              <template #prefix>
                <img :src="accountIcon" h-20px w-20px>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input v-model="passwordFormParams.password" type="password" placeholder="请输入密码" maxlength="20" clearable show-password>
              <template #prefix>
                <img :src="passwordIcon" h-20px w-20px>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane :label="TABS[1].label" :name="TABS[1].name">
        <el-form ref="mobileFormRef" :model="mobileFormParams" :rules="PHONE_FORM_RULES" mt-43px>
          <el-form-item prop="mobile">
            <el-input v-model="mobileFormParams.mobile" placeholder="请输入手机号" clearable maxlength="11">
              <template #prefix>
                <span>+86</span>
              </template>
            </el-input>
          </el-form-item>
          <div class="verify-code-wrap" flex justify-between>
            <el-form-item prop="captchaCode">
              <el-input v-model="mobileFormParams.captchaCode" placeholder="请输入验证码" clearable maxlength="6">
                <template #prefix>
                  <img :src="verifyCodeIcon" h-20px w-20px>
                </template>
              </el-input>
            </el-form-item>
            <el-button :type="verifyBtnDisabled ? 'default' : 'primary'" :disabled="verifyBtnDisabled" plain class="send-code-btn" @click="handleGetCode">
              {{ isCounting ? `${countdown}s重新获取` : '获取验证码' }}
            </el-button>
          </div>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <div h-4 flex items-center justify-between text-xs>
      <el-checkbox @change="(val: CheckboxValueType) => setAutoLogin(!!val)">
        <span text-xs text-sk-text-deep>自动登录</span>
      </el-checkbox>
      <span cursor-pointer text-sk-primary @click="handleChangeForgotPassword">忘记密码</span>
    </div>
    <el-button type="primary" mt-10 w-full :loading="btnLoading" @click="login">
      登录
    </el-button>

    <div mt-5 flex justify-between text-xs>
      <span cursor-pointer text-sk-primary @click="handleToWebsite">返回官网</span>
      <span cursor-pointer text-sk-primary @click="handleChangeSignUp">注册账户</span>
    </div>

    <!-- <div mt-5 flex justify-center text-xs text-sk-text-deep>
      <span v-for="link in FOOTER_LINKS" :key="link.name" cursor-pointer px-15px @click="handleLinkClick(link.routerName)">
        {{ link.name }}
      </span>
    </div> -->
  </div>
</template>

<style scoped>
@import '../index.css';
</style>
