<script setup lang="ts">
import { useRouter } from 'vue-router'

// const servicePhone = window.$config.servicePhone
const router = useRouter()
const currTab = inject<Ref<'signIn' | 'signUp'>>('currTab')
const userName = inject<Ref<string>>('userName')
function handleChangeSignIn() {
  currTab!.value = 'signIn'
}
function goHome() {
  router.replace({ name: 'Home' })
}
</script>

<template>
  <div bg="[rgba(255,255,255,0.95)]" w-500px rounded-5px px-12.5 pb-5 pt-7.5 shadow-lg>
    <div m-y-5 flex-center>
      <i-svg:success h-70px w-70px />
    </div>
    <p flex-x-center text-base font-bold>
      您的帐户： <span mr-2 select-text>{{ userName }}</span> 注册成功
    </p>
    <p mt-5 flex-x-center text-sm>
      恭喜您，注册成功，请使用您的注册手机号登录后及时修改密码，如有问题，请随时联系我们的客服人员
    </p>
    <div mt-12.5 flex justify-between>
      <el-button type="default" plain w-190px @click="goHome">
        返回首页
      </el-button>
      <el-button type="primary" w-190px @click="handleChangeSignIn">
        登录
      </el-button>
    </div>
  </div>
</template>

<style scoped>
@import '../index.css';
</style>
