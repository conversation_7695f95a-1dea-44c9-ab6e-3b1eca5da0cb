<script setup lang="ts">
import { getTenantToken, type TenantInfo } from '@/api/personal'

const { data = {} as TenantInfo } = defineProps<{ data: TenantInfo }>()

/**
 * 进入租户
 */
const enterTenant = async () => {
  const token = await getTenantToken(data.tenantId)
  window.location.href = `${import.meta.env.VITE_TENANT_REDIRECT_URL}?token=${token}`
}
</script>

<template>
  <div border="1 solid #e4e7ed" mb-3 mr-0 mt-2 w-295px rounded-md>
    <div flex flex-col>
      <div text="16px" font="bold" border-b="1 solid #e4e7ed" h-10 flex items-center pl-5 pr-4>
        <p class="truncate">
          {{ data.tenantName }}
        </p>
      </div>
      <div mx-5 h-78px flex items-center justify-between>
        <div h-full flex flex-col justify-around py-1>
          <div text="14px #777" flex items-center>
            <i-svg:code mr-2.5 h-4 w-4 />
            <p w-120px truncate>
              {{ data.tenantNumber }}
            </p>
          </div>
          <div text="14px #777" flex items-center>
            <i-svg:phone mr-2.5 h-4 w-4 />
            <p w-120px truncate>
              {{ data.contactMobile }}
            </p>
          </div>
        </div>
        <div border="1 solid #5180F3" text="12px #5180F3" h-28px w-20 flex-center cursor-pointer rounded-md py-2 @click="enterTenant">
          进入该租户
        </div>
      </div>
    </div>
  </div>
</template>
