<script setup lang="ts">
import type { FormInstance, FormRules, TabPaneName } from 'element-plus'
import { addEnterpriseAuthentication } from '@/api/personal/real-name-authentication'
import { ENTERPRISE_TYPE, INDUSTRY, LISTED_TYPE, YESORNO_OPTIONS } from '@/constant'
import { REG } from '@/constant/reg'
import { Back, CircleCheckFilled } from '@element-plus/icons-vue'

export interface FileResponse {
  code: number
  data: FileInfo
  msg: null
}

export interface FileInfo {
  documentNo: string
  documentName: string
  documentVersion: null
  documentLength: string
  documentUrl: string
  uploadTime: number
}
defineOptions({
  name: 'EnterpriseAuthentication',
})
const activeName = ref('basics')
const router = useRouter()
const isSubmit = ref(true)
const active = ref(1)
// const { userInfo } = useUserStore()
const basicsFormRef = useTemplateRef<FormInstance>('basicsFormRef')
const businessFormRef = useTemplateRef<FormInstance>('businessFormRef')
const baseInfo = ref({}) as Ref
const businessInfo = ref({}) as Ref
const { PHONE, CN_NUM_ENG_REGEXP, CHINESE_REGEXP, NUMBER_REGEXP } = REG
const rules = reactive<FormRules<typeof baseInfo>>({
  name: [
    { required: true, message: '请输入', trigger: 'blur' },
    { pattern: CN_NUM_ENG_REGEXP, message: '请输入中文、数字、英文', trigger: 'blur' },
  ],
  alias: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  creditCode: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  legalPersonName: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  isListed: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  staffNumRange: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  industry: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  region: [
    { required: true, message: '请输入', trigger: 'blur' },
    { pattern: CHINESE_REGEXP, message: '请输入中文', trigger: 'blur' },
  ],
  postalCode: [
    { required: true, message: '请输入', trigger: 'blur' },
    { pattern: NUMBER_REGEXP, message: '请输入数字', trigger: 'blur' },
  ],
  contactAddress: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  registAddress: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  registTime: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  website: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  contactName: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  contactPhoneNumber: [
    { required: true, message: '请输入', trigger: 'blur' },
    { pattern: PHONE, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  contactEmail: [
    { required: true, message: '请输入', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
  ],
  enterpriseType: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  companyOrgType: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  regInstitute: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  bondNum: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  listedType: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  historyNames: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  regStatus: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  regCapital: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  bureau: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  fromTime: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  toTime: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  approvedTime: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  estiblishTime: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  orgNumber: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  regNumber: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  revokeLocalDateTime: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  regLocation: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  businessScope: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  sc: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
})
async function submitForm() {
  businessFormRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        if (baseInfo.value.registTime)
          baseInfo.value.registTime = baseInfo.value.registTime.valueOf()
        if (businessInfo.value.fromTime)
          businessInfo.value.fromTime = businessInfo.value.fromTime.valueOf()
        if (businessInfo.value.toTime)
          businessInfo.value.toTime = businessInfo.value.toTime.valueOf()
        if (businessInfo.value.approvedTime)
          businessInfo.value.approvedTime = businessInfo.value.approvedTime.valueOf()
        if (businessInfo.value.estiblishTime)
          businessInfo.value.estiblishTime = businessInfo.value.estiblishTime.valueOf()
        if (businessInfo.value.revokeLocalDateTime)
          businessInfo.value.revokeLocalDateTime = businessInfo.value.revokeLocalDateTime.valueOf()
        await addEnterpriseAuthentication({
          baseInfo: baseInfo.value,
          businessInfo: businessInfo.value,
        }) as any
        active.value = 3
        isSubmit.value = false
        $message.success('提交成功')
      } catch (error) {
        console.log(error)
      }
    }
  })
}
async function handleClick(activeName: TabPaneName, oldActiveName: TabPaneName) {
  console.log(activeName, oldActiveName)
  if (activeName === 'business') {
    const validate = await basicsFormRef.value?.validate()
    if (validate)
      active.value = 2
    return validate
  } else {
    active.value = 1
    return true
  }
}
</script>

<template>
  <div class="basic-setting personal-page">
    <div class="flex">
      <el-icon :size="22" class="cursor-pointer">
        <Back @click="router.go(-1)" />
      </el-icon>
      <span class="personal-page-title ml-2.5">企业认证</span>
    </div>
    <el-steps style="max-width: 600px; margin-left: 170px;" :active="active" align-center>
      <el-step title="填写基础信息" :icon="CircleCheckFilled" />
      <el-step title="填写工商信息" :icon="CircleCheckFilled" />
      <el-step title="提交完成" :icon="CircleCheckFilled" />
    </el-steps>
    <template v-if="isSubmit">
      <el-tabs
        v-model="activeName" class="demo-tabs" :before-leave="handleClick"
      >
        <el-tab-pane label="基础信息" name="basics" />
        <el-tab-pane label="工商信息" name="business" />
      </el-tabs>
      <template v-if="activeName === 'basics'">
        <el-form ref="basicsFormRef" :model="baseInfo" :rules="rules" label-width="auto" label-position="right">
          <el-form-item label="公司名称:" prop="name">
            <el-input v-model="baseInfo.name" placeholder="请输入" :maxlength="30" clearable />
          </el-form-item>
          <el-form-item label="公司简称:" prop="alias">
            <el-input v-model="baseInfo.alias" placeholder="请输入" :maxlength="30" clearable />
          </el-form-item>
          <el-form-item label="社会统一信用代码:" prop="creditCode">
            <el-input v-model="baseInfo.creditCode" placeholder="请输入" clearable :maxlength="30" />
          </el-form-item>
          <el-form-item label="公司法定代表人:" prop="legalPersonName">
            <el-input v-model="baseInfo.legalPersonName" placeholder="请输入" clearable :maxlength="30" />
          </el-form-item>
          <el-form-item label="是否上市:" prop="isListed">
            <el-select v-model="baseInfo.isListed" placeholder="请选择">
              <el-option v-for="item in YESORNO_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="公司规模:" prop="staffNumRange">
            <el-input v-model="baseInfo.staffNumRange" placeholder="请输入" clearable :maxlength="30" />
          </el-form-item>
          <el-form-item label="所属行业:" prop="industry">
            <el-select v-model="baseInfo.industry" placeholder="请选择">
              <el-option v-for="item in INDUSTRY" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="所在区域:" prop="region">
            <el-input v-model="baseInfo.region" placeholder="请输入" clearable :maxlength="100" />
          </el-form-item>
          <el-form-item label="邮政编码:" prop="postalCode">
            <el-input v-model="baseInfo.postalCode" placeholder="请输入" clearable :maxlength="30" />
          </el-form-item>
          <el-form-item label="通讯地址:" prop="contactAddress">
            <el-input v-model="baseInfo.contactAddress" placeholder="请输入" clearable :maxlength="100" show-word-limit />
          </el-form-item>
          <el-form-item label="注册地址:" prop="registAddress">
            <el-input v-model="baseInfo.registAddress" placeholder="请输入" clearable :maxlength="100" show-word-limit />
          </el-form-item>
          <el-form-item label="创立日期:" prop="registTime">
            <el-date-picker v-model="baseInfo.registTime" type="date" placeholder="请选择" class="w-full" />
          </el-form-item>
          <el-form-item label="公司网址:" prop="website">
            <el-input v-model="baseInfo.website" placeholder="请输入" clearable :maxlength="100" show-word-limit />
          </el-form-item>
          <el-form-item label="联系人姓名:" prop="contactName">
            <el-input v-model="baseInfo.contactName" placeholder="请输入" clearable :maxlength="30" />
          </el-form-item>
          <el-form-item label="联系人电话:" prop="contactPhoneNumber">
            <el-input v-model="baseInfo.contactPhoneNumber" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="联系人邮箱:" prop="contactEmail">
            <el-input v-model="baseInfo.contactEmail" placeholder="请输入" clearable />
          </el-form-item>
        </el-form>
      </template>
      <template v-else>
        <el-form ref="businessFormRef" :model="businessInfo" :rules="rules" label-width="auto" label-position="right">
          <el-form-item label="企业名称:" prop="name">
            <el-input v-model="businessInfo.name" placeholder="请输入" :maxlength="30" clearable />
          </el-form-item>
          <el-form-item label="企业性质:" prop="enterpriseType">
            <el-select v-model="businessInfo.enterpriseType" placeholder="请选择" clearable>
              <el-option v-for="item in ENTERPRISE_TYPE" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="企业类型:" prop="companyOrgType">
            <el-input v-model="businessInfo.companyOrgType" placeholder="请输入" clearable :maxlength="30" />
          </el-form-item>
          <el-form-item label="登记机关:" prop="regInstitute">
            <el-input v-model="businessInfo.regInstitute" placeholder="请输入" clearable :maxlength="30" />
          </el-form-item>
          <el-form-item label="是否上市:" prop="isListed">
            <el-select v-model="businessInfo.isListed" placeholder="请选择">
              <el-option v-for="item in YESORNO_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="股票代码:" prop="bondNum">
            <el-input v-model="businessInfo.bondNum" placeholder="请输入" clearable :maxlength="30" />
          </el-form-item>
          <el-form-item label="上市类型:" prop="listedType">
            <el-select v-model="businessInfo.listedType" placeholder="请选择" clearable>
              <el-option v-for="item in LISTED_TYPE" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="曾用名:" prop="historyNames">
            <el-input v-model="businessInfo.historyNames" placeholder="请输入" clearable :maxlength="30" />
          </el-form-item>
          <el-form-item label="登记状态:" prop="regStatus">
            <el-input v-model="businessInfo.regStatus" placeholder="请输入" clearable :maxlength="30" />
          </el-form-item>
          <el-form-item label="注册资本:" prop="regCapital">
            <el-input v-model="businessInfo.regCapital" placeholder="请输入" clearable :maxlength="30" :precision="2" />
          </el-form-item>
          <el-form-item label="所属工商局:" prop="bureau">
            <el-input v-model="businessInfo.bureau" placeholder="请输入" clearable :maxlength="30" />
          </el-form-item>
          <el-form-item label="营业期限始:" prop="fromTime">
            <el-date-picker v-model="businessInfo.fromTime" type="date" placeholder="请选择" class="w-full" />
          </el-form-item>
          <el-form-item label="营业结束至:" prop="toTime">
            <el-date-picker v-model="businessInfo.toTime" type="date" placeholder="请选择" class="w-full" />
          </el-form-item>
          <el-form-item label="核准日期:" prop="approvedTime">
            <el-date-picker v-model="businessInfo.approvedTime" type="date" placeholder="请选择" class="w-full" />
          </el-form-item>
          <el-form-item label="法定代表人名称:" prop="legalPersonName">
            <el-input v-model="businessInfo.legalPersonName" placeholder="请输入" clearable :maxlength="30" />
          </el-form-item>
          <el-form-item label="成立日期:" prop="estiblishTime">
            <el-date-picker v-model="businessInfo.estiblishTime" type="date" placeholder="请选择" class="w-full" />
          </el-form-item>
          <el-form-item label="组织机构代码:" prop="orgNumber">
            <el-input v-model="businessInfo.orgNumber" placeholder="请输入" clearable :maxlength="30" />
          </el-form-item>
          <el-form-item label="企业注册号:" prop="regNumber">
            <el-input v-model="businessInfo.regNumber" placeholder="请输入" clearable :maxlength="30" />
          </el-form-item>
          <el-form-item label="吊销日期:" prop="revokeLocalDateTime">
            <el-date-picker v-model="businessInfo.revokeLocalDateTime" type="date" placeholder="请选择" class="w-full" />
          </el-form-item>
          <el-form-item label="统一社会信用代码:" prop="creditCode">
            <el-input v-model="businessInfo.creditCode" placeholder="请输入" clearable :maxlength="30" />
          </el-form-item>
          <el-form-item label="注册地址:" prop="regLocation">
            <el-input v-model="businessInfo.regLocation" placeholder="请输入" clearable :maxlength="100" show-word-limit />
          </el-form-item>
          <el-form-item class="avatar-upload" label="生产经营许可证:" prop="sc">
            <el-input v-model="businessInfo.sc" placeholder="请输入" clearable :maxlength="30" />
          </el-form-item>
          <el-form-item label="经营范围:" prop="businessScope" class="w-full">
            <el-input v-model="businessInfo.businessScope" placeholder="请输入" clearable :maxlength="4000" type="textarea" show-word-limit />
          </el-form-item>
        </el-form>
      </template>
      <div class="mt-6 h-9 flex justify-center">
        <el-button
          v-if="active > 1" class="but" @click="() => {
            active--
            activeName = 'basics'
          }"
        >
          上一步
        </el-button>
        <el-button
          v-if="active === 1"
          class="but" @click="() => {
            basicsFormRef?.validate(async (valid) => {
              if (valid) {
                active++
                activeName = 'business'
              }
            })
          }"
        >
          下一步
        </el-button>
        <el-button v-if="active === 2" type="primary" class="but ml-5" @click="submitForm">
          提交
        </el-button>
      </div>
    </template>
    <template v-else>
      <el-result
        class="ml-56.5 w-121.75" icon="success" sub-title="*企业实名认证请确保您所填写的信息与您的相关证件信息一致准确，
我们将在24小时内进行审核，并将相关结果进行短信通知，请确保您
的手机号码准确畅通"
      >
        <template #extra>
          <el-button
            type="primary" @click="() => {
              router.push({ name: 'EnterpriseDetails' })
            }"
          >
            确定
          </el-button>
        </template>
      </el-result>
    </template>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-descriptions__label) {
  color: #a5a5a5;
}

.but {
  width: 83px;
  height: 36px;
  border-radius: 5px;
  text-align: center;
  line-height: 36px;
  border: 1px solid #e4e4e4;
  font-size: 14px;
}

.bg-color {
  background: #5180f3;
  color: #fff;
}

.basic-setting {
  :deep(.el-form) {
    display: flex;
    flex-wrap: wrap;

    .el-form-item {
      width: 470px;
      margin: 0 0px 20px 0;

      .el-form-item__label {
        margin-bottom: 5px;
      }

      .el-form-item__content {
        > .el-input {
          height: 34px;
        }
      }
    }

    .w-full {
      width: 100%;
    }
  }
}
</style>
