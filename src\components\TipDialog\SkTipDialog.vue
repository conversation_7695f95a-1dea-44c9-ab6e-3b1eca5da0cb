<script setup lang="ts">
import WarningImg from '@/assets/imgs/warning.png'
import { ElButton, ElDialog } from 'element-plus'
import { computed, reactive, ref, toRefs } from 'vue'

type ElDialogRef = InstanceType<typeof ElDialog>

export interface Prop {
  /** 头部标题--不传默认为提示 */
  headTitle?: string
  /** 头部样式类型--不传默认为warning */
  headType?: 'base' | 'warning'
  /** 弹窗大小--不传默认为small */
  dialogSize?: 'base' | 'small'
  /** el-dialog组件的attrs */
  dialogAttrs?: ElDialogRef['$props'] & Recordable
  /** 底部插槽区域按钮配置 */
  footBtnConfig?: {
    /** 取消按钮--不传默认为true */
    isShowCancelBtn?: boolean
    /** 取消按钮文本--不传默认为取消 */
    cancelBtnText?: string
    /** 确认按钮--不传默认为true */
    isShowConfirmBtn?: boolean
    /** 确认按钮是否启用显示加载loading效果--不传默认为true  */
    isConfirmBtnLoading?: boolean
    /** 确认按钮文本--不传默认为确认 */
    confirmBtnText?: string
  }
  /** 是否是二次确认提示弹窗--不传默认为true */
  isConfirmTip?: boolean
  /** 二次确认提示弹窗配置 */
  confirmTipConfig?: {
    /** 强调内容 */
    strongContent?: string
    /** 提示内容 */
    hintContent?: string
    /** 基础内容 */
    baseContent?: string
  }
  /** js调用渲染的html内容 */
  contentHtml?: string
}

const props = withDefaults(defineProps<Prop>(), {
  headTitle: '提示',
  headType: 'warning',
  dialogSize: 'small',
  isConfirmTip: true,
})

const emit = defineEmits<{
  cancel: []
  confirm: [done: () => void, close: () => void]
}>()

const { footBtnConfig } = toRefs(props)

const isShowDialog = defineModel<boolean>({ default: false })

const headImgMap = reactive({
  base: '',
  warning: WarningImg,
})

const isConfirmLoading = ref(false)
const btnConfig = computed(() => {
  const { isShowCancelBtn = true, cancelBtnText = '取消', isShowConfirmBtn = true, isConfirmBtnLoading = true, confirmBtnText = '确认' } = footBtnConfig.value || {}

  return {
    isShowCancelBtn,
    cancelBtnText,
    isShowConfirmBtn,
    isConfirmBtnLoading,
    confirmBtnText,
  }
})
function handleCancel() {
  isShowDialog.value = false
  if (isConfirmLoading.value)
    isConfirmLoading.value = false
  emit('cancel')
}
function handleConfirm() {
  if (btnConfig.value.isConfirmBtnLoading)
    isConfirmLoading.value = true
  emit(
    'confirm',
    () => (isConfirmLoading.value = false),
    () => (isShowDialog.value = false),
  )
}
</script>

<template>
  <ElDialog
    v-model="isShowDialog"
    class="sk-tip-dialog"
    :class="`sk-tip-dialog-${dialogSize}`"
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    v-bind="dialogAttrs"
    @close="handleCancel"
  >
    <template #header>
      <div class="sk-tip-dialog__header">
        <img
          v-if="headImgMap[headType]"
          :src="headImgMap[headType]"
          class="sk-tip-dialog__header-img"
        >
        <span
          class="el-dialog__title"
          :class="headImgMap[headType] ? 'el-dialog-not-base' : ''"
        >
          {{ headTitle }}
        </span>
      </div>
    </template>
    <div
      v-if="isConfirmTip"
      class="sk-tip-confirm-body"
    >
      <span>{{ confirmTipConfig?.strongContent }}</span>
      <div>
        <span>{{ confirmTipConfig?.hintContent }}</span>
        <span>{{ confirmTipConfig?.baseContent }}</span>
      </div>
    </div>
    <slot>
      <!-- 渲染纯js窗体 -->
      <div
        v-if="contentHtml"
        v-html="contentHtml"
      />
    </slot>
    <template #footer>
      <slot name="foot-left" />
      <ElButton
        v-if="btnConfig?.isShowCancelBtn"
        @click="handleCancel"
      >
        {{ btnConfig.cancelBtnText }}
      </ElButton>
      <slot name="foot-center" />
      <ElButton
        v-if="btnConfig?.isShowConfirmBtn"
        :loading="isConfirmLoading"
        @click="handleConfirm"
      >
        {{ btnConfig.confirmBtnText }}
      </ElButton>
      <slot name="foot-right" />
    </template>
  </ElDialog>
</template>

<style lang="scss">
.sk-tip-dialog {
  .sk-tip-dialog__header {
    display: flex;
    align-items: center;

    .sk-tip-dialog__header-img {
      width: 16px;
      height: 16px;
      margin-right: 10px;
    }
  }

  .el-dialog__body {
    padding: 20px 20px 10px;

    .el-form-item__label {
      padding: 0;
    }

    .sk-tip-confirm-body {
      margin-top: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      color: var(--sk-text-deep);
      font-weight: 400;
      font-size: 16px;

      > span {
        font-weight: 700;
      }

      > div {
        margin-top: 10px;

        > span:first-child {
          color: var(--el-color-error);
        }
      }
    }
  }
}

.el-dialog.sk-tip-dialog-base {
  width: 950px;
}

.el-dialog.sk-tip-dialog-small {
  width: 480px;
}
</style>
