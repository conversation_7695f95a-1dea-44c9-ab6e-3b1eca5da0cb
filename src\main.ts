import App from '@/App.vue'
import setupDirectives from '@/directives'
import i18n from '@/plugins/i18n'
import pinia from '@/plugins/pinia'
import VXETable from '@/plugins/vxe-table'
import { setupRouter } from '@/router'
import { ElMessage, ElMessageBox } from 'element-plus'
import 'virtual:uno.css'
import '@/styles/index.css'
import '@/styles/common.scss'

// 如果您正在使用CDN引入，请删除下面一行。
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

const app = createApp(App)
setupRouter(app)
setupDirectives(app)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.use(i18n).use(pinia).use(VXETable).mount('#app')

// 添加全局属性
window.$message = ElMessage
window.$messageBox = ElMessageBox
