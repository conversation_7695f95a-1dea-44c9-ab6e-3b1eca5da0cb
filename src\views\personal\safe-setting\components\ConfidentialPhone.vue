<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { getSmsCode, updateMobile } from '@/api/personal/safe-setting'
import SkTipDialog from '@/components/TipDialog/SkTipDialog.vue'
import { REG } from '@/constant/reg'

interface Prop {
  mobile: string
}

const props = defineProps<Prop>()

const phone = ref('')
watchEffect(() => {
  phone.value = props.mobile
})

const formRef = useTemplateRef<FormInstance>('formRef')
const form = reactive({
  mobile: '',
  code: '',
})

const isShowDialog = ref(false)

// const phoneRepeatValidator = (_rule, value: string, callback: (error?: Error) => void) => {
//   if (value === phone.value) {
//     callback(new Error('不可输入与目前一致的手机号码'))
//     return
//   }
//   callback()
// }
const rules = reactive<FormRules<typeof form>>({
  mobile: [
    { required: true, message: '请输入', trigger: 'blur' },
    { pattern: REG.PHONE, message: '请输入正确的手机号码', trigger: 'blur' },
    // { validator: phoneRepeatValidator, trigger: 'blur' }
  ],
  code: [{ required: true, message: '请输入', trigger: 'blur' }],
})

const codeStatus = ref(0) // 0 未校验成功手机号 1 校验成功手机号，可点击获取验证码 2 已点击获取验证码

let timer: ReturnType<typeof setInterval>

function handleNewPhoneInput() {
  form.mobile = form.mobile.replace(/\s+/g, '')

  REG.PHONE.test(form.mobile) ? (codeStatus.value = 1) : (codeStatus.value = 0)
  timer && clearInterval(timer)
}

const countDown = ref(0)
function handleGetCode() {
  if (codeStatus.value === 0)
    return
  getSmsCodeApi()
}

async function getSmsCodeApi() {
  await getSmsCode(form.mobile)
  codeStatus.value = 2
  countDown.value = 60
  $message.success('验证码已发送')
  timer = setInterval(() => {
    if (countDown.value > 0) {
      countDown.value--
    } else {
      clearInterval(timer)
      codeStatus.value = 1
    }
  }, 1000)
}

/** 手机号脱敏 */
function maskPhoneNumber(phoneNumber: string) {
  // 检查手机号格式是否符合
  const phoneRegex = /^(\d{3})(\d{4})(\d{4})$/
  const match = phoneNumber.match(phoneRegex)

  if (match) {
    // 用星号替换中间的四位数字
    return `${match[1]}****${match[3]}`
  } else {
    throw new Error('手机号格式不正确')
  }
}

function handleCancel() {
  form.mobile = ''
  form.code = ''
  formRef.value?.resetFields()
  codeStatus.value = 0
  countDown.value = 0
  timer && clearInterval(timer)
}
async function handleConfirm(done: () => void, close: () => void) {
  formRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        await updateMobile(form)
        $message.success('修改成功')
        phone.value = maskPhoneNumber(form.mobile)
        close()
      } finally {
        done()
      }
    } else {
      done()
    }
  })
}
</script>

<template>
  <div class="confidential-phone">
    <div class="content">
      <span class="title">密保手机</span>
      <span class="info">
        已绑定手机
        <span class="ml-20">{{ phone }}</span>
      </span>
    </div>
    <el-button
      type="primary"
      link
      @click="isShowDialog = true"
    >
      修改
    </el-button>
    <SkTipDialog
      v-model="isShowDialog"
      head-title="修改密保手机"
      :is-confirm-tip="false"
      :foot-btn-config="{ isShowCancelBtn: false }"
      @cancel="handleCancel"
      @confirm="handleConfirm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="110px"
      >
        <el-form-item
          label="新手机号："
          prop="mobile"
          class="mb-30"
        >
          <el-input
            v-model="form.mobile"
            placeholder="请输入"
            clearable
            @input="handleNewPhoneInput"
          />
        </el-form-item>
        <el-form-item
          label="请输入验证码："
          prop="code"
          class="mb-14"
        >
          <el-input
            v-model="form.code"
            class="verification-code"
            placeholder="请输入"
            clearable
            @input="() => (form.code = form.code.replace(/\s+/g, ''))"
          >
            <template #append>
              <span
                v-show="codeStatus === 0 || codeStatus === 1"
                class="code-text"
                :class="{ 'is-active': codeStatus === 0 }"
                @click="handleGetCode"
              >
                获取验证码
              </span>
              <span
                v-show="codeStatus === 2"
                class="is-active"
              >
                已获取验证码 {{ countDown }}
              </span>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
    </SkTipDialog>
  </div>
</template>

<style lang="scss" scoped>
.confidential-phone {
  :deep(.el-dialog__body) {
    padding: 20px 52px 0 30px !important;
  }

  .mb-14 {
    margin-bottom: 14px;
  }

  .ml-20 {
    margin-left: 20px;
  }

  .mb-30 {
    margin-bottom: 30px !important;
  }

  .verification-code {
    border: 1px solid var(--sk-E4);
    border-radius: 5px;

    :deep(.el-input__wrapper) {
      box-shadow: none !important;
    }

    :deep(.el-input-group__append) {
      width: auto;
      background-color: #fff;
      box-shadow: none;
      padding: 0 10px;
      box-sizing: content-box;
      white-space: nowrap;
      position: relative;

      .code-text {
        cursor: pointer;
        color: var(--el-color-primary);
      }

      .is-active {
        color: var(--sk-text-light);
      }
    }

    :deep(.el-input-group__append::before) {
      content: '';
      width: 1px;
      height: 16px;
      background-color: var(--sk-E4);
      position: absolute;
      left: 0;
    }
  }
}
</style>
