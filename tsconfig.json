{
  "compilerOptions": {
    "target": "ESNext",
    "jsx": "preserve",
    "lib": ["ESNext", "DOM", "DOM.Iterable"],
    "moduleDetection": "force",
    "useDefineForClassFields": true,

    /* Paths */
    "baseUrl": "./",
    "module": "ESNext",

    /* Bundler mode */
    "moduleResolution": "bundler",
    "paths": {
      "@/*": ["./src/*"],
      "~/*": ["./*"]
    },
    "resolveJsonModule": true,
    "types": ["@intlify/unplugin-vue-i18n/types", "element-plus/global", "vite/client", "unplugin-icons/types/vue"],
    "allowImportingTsExtensions": true,

    /* Linting */
    "strict": true,
    "noFallthroughCasesInSwitch": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noEmit": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "skipLibCheck": true
  },
  "include": ["./**/*.ts", "./**/*.tsx", "./**/*.vue", "types/**/*.d.ts"],
  "exclude": ["node_modules", "dist", "public", "uno.config.ts"]
}
