<script setup lang="ts">
import { getNotificationSetting } from '@/api/personal/notification-setting'
import MessageNotification from './components/MessageNotification.vue'
import SystemNotification from './components/SystemNotification.vue'
import TodoNotification from './components/TodoNotification.vue'

interface NotificationSetting {
  msgNotify: EnableNum
  systemNotify: EnableNum
  todoNotify: EnableNum
}

defineOptions({
  name: 'NotificationSetting',
})

const isObtained = ref(false)
const notificationSetting: NotificationSetting = reactive({
  msgNotify: 0,
  systemNotify: 0,
  todoNotify: 0,
})

async function getNotificationSettingApi() {
  const data = await getNotificationSetting()
  const keys = Reflect.ownKeys(notificationSetting) as (keyof NotificationSetting)[]
  for (const key of keys) {
    notificationSetting[key] = data[key]
  }
  isObtained.value = true
}

onBeforeMount(() => {
  getNotificationSettingApi()
})
</script>

<template>
  <div class="personal-page notification-setting">
    <span class="personal-page-title">通知设置</span>
    <MessageNotification
      v-model="notificationSetting.msgNotify"
      :is-obtained="isObtained"
    />
    <el-divider />
    <SystemNotification
      v-model="notificationSetting.systemNotify"
      :is-obtained="isObtained"
    />
    <el-divider />
    <TodoNotification
      v-model="notificationSetting.todoNotify"
      :is-obtained="isObtained"
    />
  </div>
</template>

<style lang="scss" scoped>
.notification-setting {
  > div {
    display: flex;
    justify-content: space-between;
    align-items: center;

    :deep(.content) {
      font-size: 14px;
      display: flex;
      flex-direction: column;

      .title {
        font-weight: 700;
        margin-bottom: 10px;
      }

      .info {
        color: var(--sk-text);
      }
    }
  }

  :deep(.el-divider) {
    margin: 20px 0 30px;
    border-color: var(--sk-E4);
  }
}
</style>
